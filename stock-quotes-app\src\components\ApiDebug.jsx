import { useState } from 'react';
import { getAvailableProviders } from '../config/apiConfig';
import { getStockQuote, getDailyStockData } from '../services/multiApiService';

const ApiDebug = () => {
  const [testResult, setTestResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState('ALPHA_VANTAGE');

  const availableProviders = getAvailableProviders();

  const testApi = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      console.log(`Testing ${selectedProvider} API...`);

      const result = await getStockQuote('AAPL', selectedProvider);
      setTestResult({
        success: true,
        data: result,
        message: `${selectedProvider} API working!`,
        provider: selectedProvider
      });
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message,
        message: `${selectedProvider} API test failed`,
        provider: selectedProvider
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ 
      background: '#f8f9fa', 
      padding: '1rem', 
      margin: '1rem 0', 
      border: '1px solid #dee2e6',
      borderRadius: '8px'
    }}>
      <h3>🔧 API Debug Panel</h3>
      <p>Available Providers: {availableProviders.length}</p>

      <div style={{ marginBottom: '1rem' }}>
        <label>Test Provider: </label>
        <select
          value={selectedProvider}
          onChange={(e) => setSelectedProvider(e.target.value)}
          style={{ marginLeft: '0.5rem', padding: '0.25rem' }}
        >
          {availableProviders.map(provider => (
            <option key={provider.key} value={provider.key}>
              {provider.name}
            </option>
          ))}
        </select>
      </div>

      <button
        onClick={testApi}
        disabled={isLoading || availableProviders.length === 0}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        {isLoading ? 'Testing...' : `Test ${selectedProvider} API`}
      </button>

      {testResult && (
        <div style={{ 
          marginTop: '1rem', 
          padding: '0.5rem',
          backgroundColor: testResult.success ? '#d4edda' : '#f8d7da',
          border: `1px solid ${testResult.success ? '#c3e6cb' : '#f5c6cb'}`,
          borderRadius: '4px'
        }}>
          <strong>{testResult.message}</strong>
          {testResult.data && (
            <div>
              <p>AAPL Price: ${testResult.data.price}</p>
              <p>Change: {testResult.data.changePercent}</p>
            </div>
          )}
          {testResult.error && (
            <p>Error: {testResult.error}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default ApiDebug;
