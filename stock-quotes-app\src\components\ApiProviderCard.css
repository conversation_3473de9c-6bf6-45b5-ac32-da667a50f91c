.api-provider-card {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.api-provider-card:hover:not(.disabled) {
  border-color: #3498db;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
  transform: translateY(-2px);
}

.api-provider-card.selected {
  border-color: #27ae60;
  background: linear-gradient(135deg, #d5f4e6 0%, #a8e6cf 100%);
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

.api-provider-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f8f9fa;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.card-badges {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.selected-badge {
  background-color: #d5f4e6;
  color: #27ae60;
}

.available-badge {
  background-color: #d4edda;
  color: #155724;
}

.unavailable-badge {
  background-color: #f8d7da;
  color: #721c24;
}

.card-content {
  margin-bottom: 1rem;
}

.description {
  color: #7f8c8d;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.card-actions {
  margin-bottom: 1rem;
}

.select-button {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-button:not(.selected) {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.select-button:not(.selected):hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
  transform: translateY(-1px);
}

.select-button.selected {
  background-color: #27ae60;
  color: white;
  cursor: default;
}

.select-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.setup-actions {
  text-align: center;
}

.setup-message {
  color: #e74c3c;
  margin: 0 0 0.75rem 0;
  font-weight: 600;
  font-size: 0.9rem;
}

.signup-button {
  display: inline-block;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.signup-button:hover {
  background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
  transform: translateY(-1px);
}

.card-footer {
  border-top: 1px solid #e1e8ed;
  padding-top: 0.75rem;
  margin-top: 1rem;
}

.card-footer small {
  color: #95a5a6;
  font-size: 0.8rem;
  word-break: break-all;
}

@media (max-width: 768px) {
  .api-provider-card {
    padding: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .card-badges {
    align-items: flex-start;
  }
  
  .card-header h3 {
    font-size: 1.1rem;
  }
}
