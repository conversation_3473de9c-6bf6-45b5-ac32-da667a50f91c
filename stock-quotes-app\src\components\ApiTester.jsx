import { useState } from 'react';
import { useApi } from '../context/ApiContext';
import { getStockQuote, getDailyStockData } from '../services/multiApiService';
import './ApiTester.css';

const ApiTester = () => {
  const { selectedProvider: globalProvider, availableProviders } = useApi();
  const [testSymbol, setTestSymbol] = useState('AAPL');
  const [testType, setTestType] = useState('quote');
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState(null);

  // Use global provider or fall back to first available
  const selectedProvider = globalProvider || availableProviders[0]?.key || '';

  const runTest = async () => {
    if (!selectedProvider || !testSymbol) return;

    setIsLoading(true);
    setTestResults(null);

    const startTime = Date.now();

    try {
      let result;
      if (testType === 'quote') {
        result = await getStockQuote(testSymbol.toUpperCase(), selectedProvider);
      } else {
        result = await getDailyStockData(testSymbol.toUpperCase(), selectedProvider);
      }

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      setTestResults({
        success: true,
        data: result,
        responseTime,
        provider: selectedProvider,
        symbol: testSymbol.toUpperCase(),
        testType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      setTestResults({
        success: false,
        error: error.message,
        responseTime,
        provider: selectedProvider,
        symbol: testSymbol.toUpperCase(),
        testType,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatData = (data) => {
    if (!data) return 'No data returned';
    
    if (testType === 'quote') {
      return (
        <div className="quote-data">
          <div className="data-row">
            <span className="label">Symbol:</span>
            <span className="value">{data.symbol}</span>
          </div>
          <div className="data-row">
            <span className="label">Price:</span>
            <span className="value">${data.price}</span>
          </div>
          <div className="data-row">
            <span className="label">Change:</span>
            <span className={`value ${data.change >= 0 ? 'positive' : 'negative'}`}>
              {data.change >= 0 ? '+' : ''}${data.change} ({data.changePercent})
            </span>
          </div>
          <div className="data-row">
            <span className="label">Volume:</span>
            <span className="value">{data.volume?.toLocaleString()}</span>
          </div>
        </div>
      );
    } else {
      return (
        <div className="historical-data">
          <div className="data-summary">
            <p><strong>Data Points:</strong> {data.length}</p>
            <p><strong>Date Range:</strong> {data[data.length - 1]?.date} to {data[0]?.date}</p>
          </div>
          <div className="data-sample">
            <h4>Latest 3 Days:</h4>
            {data.slice(0, 3).map((item, index) => (
              <div key={index} className="sample-row">
                <span className="date">{item.date}</span>
                <span className="price">Close: ${item.close}</span>
                <span className="volume">Vol: {item.volume?.toLocaleString()}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
  };

  return (
    <div className="api-tester">
      <div className="tester-info">
        <h3>🧪 API Testing</h3>
        <p>
          Testing with: <strong>{availableProviders.find(p => p.key === selectedProvider)?.name || 'No provider selected'}</strong>
          {!globalProvider && <span className="warning"> (No global provider selected - using first available)</span>}
        </p>
      </div>

      <div className="tester-controls">

        <div className="control-group">
          <label htmlFor="test-type">Test Type:</label>
          <select
            id="test-type"
            value={testType}
            onChange={(e) => setTestType(e.target.value)}
            className="control-input"
          >
            <option value="quote">Current Quote</option>
            <option value="historical">Historical Data</option>
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="test-symbol">Stock Symbol:</label>
          <input
            id="test-symbol"
            type="text"
            value={testSymbol}
            onChange={(e) => setTestSymbol(e.target.value.toUpperCase())}
            placeholder="e.g., AAPL"
            className="control-input"
          />
        </div>

        <button
          onClick={runTest}
          disabled={isLoading || !selectedProvider || !testSymbol}
          className="test-button"
        >
          {isLoading ? 'Testing...' : `Test ${testType === 'quote' ? 'Quote' : 'Historical'} API`}
        </button>
      </div>

      {testResults && (
        <div className={`test-results ${testResults.success ? 'success' : 'error'}`}>
          <div className="results-header">
            <h3>
              {testResults.success ? '✅ Test Successful' : '❌ Test Failed'}
            </h3>
            <div className="results-meta">
              <span>Provider: {testResults.provider}</span>
              <span>Symbol: {testResults.symbol}</span>
              <span>Response Time: {testResults.responseTime}ms</span>
              <span>Time: {new Date(testResults.timestamp).toLocaleTimeString()}</span>
            </div>
          </div>

          <div className="results-content">
            {testResults.success ? (
              <div className="success-content">
                <h4>Response Data:</h4>
                {formatData(testResults.data)}
              </div>
            ) : (
              <div className="error-content">
                <h4>Error Details:</h4>
                <p className="error-message">{testResults.error}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {availableProviders.length === 0 && (
        <div className="no-providers">
          <h3>⚠️ No API Providers Available</h3>
          <p>Please configure at least one API key in your .env file to enable testing.</p>
        </div>
      )}
    </div>
  );
};

export default ApiTester;
