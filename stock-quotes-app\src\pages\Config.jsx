import { useState } from 'react';
import { useApi } from '../context/ApiContext';
import ApiProviderCard from '../components/ApiProviderCard';
import ApiTester from '../components/ApiTester';
import { API_PROVIDERS, API_KEYS } from '../config/apiConfig';
import './Config.css';

const Config = () => {
  const { selectedProvider, availableProviders, changeProvider } = useApi();
  const [activeTab, setActiveTab] = useState('providers');

  const allProviders = Object.values(API_PROVIDERS);

  return (
    <div className="config-page">
      <div className="config-header">
        <h1>⚙️ API Configuration</h1>
        <p>Manage your stock data API providers and test connections</p>
      </div>

      <div className="config-tabs">
        <button 
          className={`tab-button ${activeTab === 'providers' ? 'active' : ''}`}
          onClick={() => setActiveTab('providers')}
        >
          📡 API Providers
        </button>
        <button 
          className={`tab-button ${activeTab === 'testing' ? 'active' : ''}`}
          onClick={() => setActiveTab('testing')}
        >
          🧪 API Testing
        </button>
        <button 
          className={`tab-button ${activeTab === 'status' ? 'active' : ''}`}
          onClick={() => setActiveTab('status')}
        >
          📊 Status Overview
        </button>
      </div>

      <div className="config-content">
        {activeTab === 'providers' && (
          <div className="providers-section">
            <div className="section-header">
              <h2>Available API Providers</h2>
              <p>Select your preferred stock data provider. The selected provider will be used across all features.</p>
            </div>
            
            <div className="current-selection">
              <h3>Current Selection</h3>
              {selectedProvider ? (
                <div className="selected-provider">
                  <span className="provider-name">
                    {API_PROVIDERS[selectedProvider]?.name}
                  </span>
                  <span className="provider-status">✅ Active</span>
                </div>
              ) : (
                <div className="no-provider">
                  <span className="provider-name">No provider selected</span>
                  <span className="provider-status">❌ Inactive</span>
                </div>
              )}
            </div>

            <div className="providers-grid">
              {allProviders.map(provider => (
                <ApiProviderCard
                  key={provider.key}
                  provider={provider}
                  isSelected={selectedProvider === provider.key}
                  isAvailable={availableProviders.some(p => p.key === provider.key)}
                  onSelect={() => changeProvider(provider.key)}
                />
              ))}
            </div>
          </div>
        )}

        {activeTab === 'testing' && (
          <div className="testing-section">
            <div className="section-header">
              <h2>API Testing</h2>
              <p>Test your API connections and verify they're working correctly.</p>
              {selectedProvider && (
                <div className="current-provider-info">
                  <span className="info-label">Testing with:</span>
                  <span className="info-value">{API_PROVIDERS[selectedProvider]?.name}</span>
                </div>
              )}
            </div>

            <ApiTester />
          </div>
        )}

        {activeTab === 'status' && (
          <div className="status-section">
            <div className="section-header">
              <h2>API Status Overview</h2>
              <p>View the configuration status of all API providers.</p>
            </div>
            
            <div className="status-grid">
              {allProviders.map(provider => {
                const apiKey = API_KEYS[provider.key];
                const isConfigured = apiKey && !apiKey.includes('your-');
                const isAvailable = availableProviders.some(p => p.key === provider.key);
                
                return (
                  <div key={provider.key} className="status-card">
                    <div className="status-header">
                      <h3>{provider.name}</h3>
                      <span className={`status-badge ${isConfigured ? 'configured' : 'not-configured'}`}>
                        {isConfigured ? '✅ Configured' : '❌ Not Configured'}
                      </span>
                    </div>
                    <div className="status-details">
                      <p><strong>Description:</strong> {provider.description}</p>
                      <p><strong>API Key:</strong> {isConfigured ? 'Present' : 'Missing'}</p>
                      <p><strong>Status:</strong> {isAvailable ? 'Available' : 'Unavailable'}</p>
                      {!isConfigured && (
                        <a href={provider.signupUrl} target="_blank" rel="noopener noreferrer" className="signup-link">
                          Get API Key →
                        </a>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Config;
