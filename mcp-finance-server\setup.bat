@echo off
echo 🚀 Setting up MCP Finance Server...

echo.
echo 📦 Installing dependencies...
npm install

echo.
echo 📋 Copying environment template...
if not exist .env (
    copy .env.example .env
    echo ✅ Created .env file - please edit it with your API keys
) else (
    echo ⚠️  .env file already exists
)

echo.
echo 🧪 Testing configuration...
node src/test.js

echo.
echo 🎉 Setup complete!
echo.
echo Next steps:
echo 1. Edit .env file with your API keys
echo 2. Run: npm test
echo 3. Run: npm start
echo.
pause
