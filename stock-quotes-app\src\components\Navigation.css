.navigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  padding: 0 2rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  text-decoration: none;
  color: #2c3e50;
  font-weight: 600;
  font-size: 1rem;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.05);
}

.nav-link.active {
  color: #3498db;
  border-bottom-color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 2px 2px 0 0;
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-link {
    padding: 0.75rem 0.75rem;
    font-size: 0.85rem;
    flex: 1;
    text-align: center;
    min-width: 0;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 0.25rem;
  }

  .nav-link {
    padding: 0.6rem 0.5rem;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Stack navigation vertically on very small screens if needed */
  .nav-container.vertical {
    flex-direction: column;
  }

  .nav-container.vertical .nav-link {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-right: none;
  }
}
