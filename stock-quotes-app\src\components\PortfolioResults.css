.portfolio-results {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.portfolio-results.loading {
  text-align: center;
  padding: 3rem;
}

.portfolio-results.error {
  background-color: #fee;
  border: 1px solid #fcc;
  text-align: center;
}

.portfolio-results.error h3 {
  color: #c33;
  margin-bottom: 0.5rem;
}

.portfolio-results.error p {
  color: #a33;
}

.results-header {
  text-align: center;
  margin-bottom: 2rem;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 1rem;
}

.results-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.date-range {
  color: #7f8c8d;
  font-size: 1.1rem;
  font-weight: 600;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem;
  border-radius: 10px;
  text-align: center;
  border: 1px solid #dee2e6;
}

.summary-card h3 {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 0.5rem 0;
}

.summary-card .amount {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.summary-card .percentage {
  font-size: 1.1rem;
  font-weight: 600;
}

.positive {
  color: #27ae60 !important;
}

.negative {
  color: #e74c3c !important;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-container {
  background: #fafbfc;
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.chart-container h3 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  text-align: center;
  font-size: 1.1rem;
}

.holdings-table {
  margin-top: 2rem;
}

.holdings-table h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

td {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.9rem;
}

tr:nth-child(even) {
  background-color: #f8f9fa;
}

tr:hover {
  background-color: #e3f2fd;
}

.symbol {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 1024px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .portfolio-results {
    padding: 1rem;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .summary-card .amount {
    font-size: 1.5rem;
  }
  
  table {
    font-size: 0.8rem;
  }
  
  th, td {
    padding: 0.5rem 0.25rem;
  }
  
  .table-container {
    font-size: 0.8rem;
  }
}
