.country-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.country-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.country-select {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.country-select:focus {
  outline: none;
  border-color: #3498db;
}

.country-select:hover {
  border-color: #bdc3c7;
}

.country-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.country-info small {
  color: #7f8c8d;
  font-style: italic;
}

@media (max-width: 768px) {
  .country-select {
    font-size: 1.1rem;
    padding: 1rem;
  }
}
