# 📈 Stock Quotes App

A React-based web application that displays real-time stock quotes and charts using the Alpha Vantage API.

## Features

- 🔍 Search for stock quotes by ticker symbol
- 📊 Real-time stock price information
- 📈 Interactive price charts with historical data
- 📱 Responsive design for mobile and desktop
- ⚡ Fast and modern React + Vite setup

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Get Your Alpha Vantage API Key

1. Go to [Alpha Vantage API Key Registration](https://www.alphavantage.co/support/#api-key)
2. Fill out the form to get your free API key
3. Copy your API key

### 3. Configure Your API Key (Environment Variables)

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Edit the `.env` file and replace `your-actual-api-key-here` with your actual API key:
```bash
VITE_ALPHA_VANTAGE_API_KEY=your-actual-api-key-here
```

**Important**: The `.env` file is ignored by git to keep your API key secure!

### 4. Run the Application

```bash
npm run dev
```

The app will be available at `http://localhost:5173`

## Usage

1. Enter a stock ticker symbol (e.g., AAPL, GOOGL, MSFT, TSLA)
2. Click "Get Quote" or press Enter
3. View the current stock price and details
4. Scroll down to see the price chart with historical data

## Popular Stock Symbols to Try

- **AAPL** - Apple Inc.
- **GOOGL** - Alphabet Inc. (Google)
- **MSFT** - Microsoft Corporation
- **TSLA** - Tesla Inc.
- **AMZN** - Amazon.com Inc.
- **META** - Meta Platforms Inc. (Facebook)
- **NVDA** - NVIDIA Corporation

## Technologies Used

- **React 19** - Frontend framework
- **Vite** - Build tool and dev server
- **Axios** - HTTP client for API requests
- **Recharts** - Chart library for data visualization
- **Alpha Vantage API** - Stock market data provider

## API Limitations

- Free Alpha Vantage API allows 25 requests per day
- Data updates may have a slight delay
- For production use, consider upgrading to a paid plan

## Security Note

⚠️ **Important**: This setup stores the API key in the frontend code, which is visible to users. For production applications, consider:

- Using environment variables
- Implementing a backend proxy to handle API calls
- Using server-side rendering to keep API keys secure

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
