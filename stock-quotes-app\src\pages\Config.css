.config-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.config-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.config-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
}

.config-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.config-tabs {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 0.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #7f8c8d;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background-color: #f8f9fa;
  color: #2c3e50;
}

.tab-button.active {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.config-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.section-header p {
  color: #7f8c8d;
  margin: 0;
}

.current-provider-info {
  background: #e8f5e8;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-left: 4px solid #27ae60;
}

.info-label {
  color: #7f8c8d;
  font-weight: 600;
}

.info-value {
  color: #27ae60;
  font-weight: bold;
}

.current-selection {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #3498db;
}

.current-selection h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.selected-provider, .no-provider {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.provider-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.provider-status {
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.status-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 1.5rem;
  background: #fafbfc;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.status-header h3 {
  margin: 0;
  color: #2c3e50;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-badge.configured {
  background-color: #d5f4e6;
  color: #27ae60;
}

.status-badge.not-configured {
  background-color: #fdeaea;
  color: #e74c3c;
}

.status-details p {
  margin: 0.5rem 0;
  color: #7f8c8d;
}

.status-details strong {
  color: #2c3e50;
}

.signup-link {
  display: inline-block;
  margin-top: 0.5rem;
  color: #3498db;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signup-link:hover {
  color: #2980b9;
}

@media (max-width: 768px) {
  .config-header {
    padding: 1.5rem;
  }
  
  .config-header h1 {
    font-size: 2rem;
  }
  
  .config-tabs {
    flex-direction: column;
  }
  
  .tab-button {
    margin-bottom: 0.5rem;
  }
  
  .config-content {
    padding: 1rem;
  }
  
  .providers-grid, .status-grid {
    grid-template-columns: 1fr;
  }
  
  .selected-provider, .no-provider {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
