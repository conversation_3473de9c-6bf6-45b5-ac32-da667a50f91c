{"quoteResponse": {"result": [{"language": "en-US", "region": "US", "quoteType": "EQUITY", "typeDisp": "Equity", "quoteSourceName": "Nasdaq Real Time Price", "triggerable": true, "customPriceAlertConfidence": "HIGH", "quoteSummary": {"summaryDetail": {"maxAge": 1, "priceHint": 2, "previousClose": 115.69, "open": 117.685, "dayLow": 115.97, "dayHigh": 118.38, "regularMarketPreviousClose": 115.69, "regularMarketOpen": 117.685, "regularMarketDayLow": 115.97, "regularMarketDayHigh": 118.38, "exDividendDate": 798940800, "payoutRatio": 0, "beta": 1.993, "trailingPE": 84.81022, "forwardPE": 22.782354, "volume": 26880034, "regularMarketVolume": 26880034, "averageVolume": 40442604, "averageVolume10days": 32624800, "averageDailyVolume10Day": 32624800, "bid": 116.04, "ask": 116.38, "bidSize": 800, "askSize": 800, "marketCap": 188390473728, "fiftyTwoWeekLow": 76.48, "fiftyTwoWeekHigh": 187.28, "priceToSalesTrailing12Months": 6.7888455, "fiftyDayAverage": 102.2468, "twoHundredDayAverage": 125.3216, "trailingAnnualDividendRate": 0, "trailingAnnualDividendYield": 0, "currency": "USD", "fromCurrency": null, "toCurrency": null, "lastMarket": null, "coinMarketCapLink": null, "algorithm": null, "tradeable": false}, "earnings": {"maxAge": 86400, "earningsChart": {"quarterly": [{"date": "2Q2024", "actual": 0.69, "estimate": 0.68141}, {"date": "3Q2024", "actual": 0.92, "estimate": 0.91582}, {"date": "4Q2024", "actual": 1.09, "estimate": 1.08606}, {"date": "1Q2025", "actual": 0.96, "estimate": 0.93359}], "currentQuarterEstimate": 0.58202, "currentQuarterEstimateDate": "2Q", "currentQuarterEstimateYear": 2025, "earningsDate": [1753700340, 1754049600], "isEarningsDateEstimate": true}, "financialsChart": {"yearly": [{"date": 2021, "revenue": 16434000000, "earnings": 3435000000}, {"date": 2022, "revenue": 23601000000, "earnings": 5504000000}, {"date": 2023, "revenue": 22680000000, "earnings": 4302000000}, {"date": 2024, "revenue": 25785000000, "earnings": 5420000000}], "quarterly": [{"date": "2Q2024", "revenue": 5835000000, "earnings": 1126000000}, {"date": "3Q2024", "revenue": 6819000000, "earnings": 1504000000}, {"date": "4Q2024", "revenue": 7658000000, "earnings": 1777000000}, {"date": "1Q2025", "revenue": 7438000000, "earnings": 1566000000}]}, "financialCurrency": "USD"}}, "priceHint": 2, "totalCash": 7310000100, "floatShares": 1610103008, "ebitda": 5878000128, "shortRatio": 1.13, "targetPriceHigh": 200, "targetPriceLow": 95, "targetPriceMean": 128.29115, "targetPriceMedian": 125, "heldPercentInsiders": 0.522, "heldPercentInstitutions": 68.201, "postMarketChangePercent": 0.404511, "postMarketTime": 1749254393, "postMarketPrice": 116.66, "postMarketChange": 0.470001, "regularMarketChange": 0.5, "regularMarketChangePercent": 0.432189, "regularMarketTime": 1749240001, "regularMarketPrice": 116.19, "regularMarketDayHigh": 118.38, "regularMarketDayRange": "115.97 - 118.38", "regularMarketDayLow": 115.97, "regularMarketVolume": 26880034, "sharesShort": 48465479, "sharesShortPrevMonth": 44440539, "shortPercentFloat": 3, "regularMarketPreviousClose": 115.69, "bid": 116.04, "ask": 116.38, "bidSize": 8, "askSize": 8, "exchange": "NMS", "market": "us_market", "messageBoardId": "finmb_168864", "fullExchangeName": "NasdaqGS", "shortName": "Advanced Micro Devices, Inc.", "longName": "Advanced Micro Devices, Inc.", "financialCurrency": "USD", "regularMarketOpen": 117.685, "averageDailyVolume3Month": 40442604, "averageDailyVolume10Day": 32624800, "beta": 1.993, "fiftyTwoWeekLowChange": 39.71, "fiftyTwoWeekLowChangePercent": 0.51922065, "fiftyTwoWeekRange": "76.48 - 187.28", "fiftyTwoWeekHighChange": -71.09, "fiftyTwoWeekHighChangePercent": -0.37959203, "fiftyTwoWeekLow": 76.48, "fiftyTwoWeekHigh": 187.28, "exDividendDate": 798940800, "earningsTimestamp": 1746562537, "earningsTimestampStart": 1753700340, "earningsTimestampEnd": 1754049600, "trailingAnnualDividendRate": 0, "trailingPE": 84.81022, "trailingAnnualDividendYield": 0, "revenue": 27750000600, "priceToSales": 6.7888455, "marketState": "CLOSED", "epsTrailingTwelveMonths": 1.37, "epsForward": 5.1, "epsCurrentYear": 3.99112, "epsNextQuarter": 1.13428, "priceEpsCurrentYear": 29.11213, "priceEpsNextQuarter": 102.43503, "sharesOutstanding": 1621400064, "bookValue": 35.817, "fiftyDayAverage": 102.2468, "fiftyDayAverageChange": 13.943199, "fiftyDayAverageChangePercent": 0.13636807, "twoHundredDayAverage": 125.3216, "twoHundredDayAverageChange": -9.131599, "twoHundredDayAverageChangePercent": -0.07286533, "marketCap": 188390473728, "forwardPE": 22.782354, "priceToBook": 3.2439902, "sourceInterval": 15, "exchangeDataDelayedBy": 0, "exchangeTimezoneName": "America/New_York", "exchangeTimezoneShortName": "EDT", "pageViews": {"midTermTrend": "UP", "longTermTrend": "UP", "shortTermTrend": "DOWN"}, "gmtOffSetMilliseconds": -14400000, "esgPopulated": false, "tradeable": false, "cryptoTradeable": false, "currency": "USD", "components": ["^DWUSSR", "^GMB", "^GMBNTR", "^IXCO", "^IXIC", "^NQCRD", "^NQCRDN", "^NQDM9000LM", "^NQDM9000LMAUD", "^NQDM9000LMAUDN", "^NQDM9000LMCAD", "^NQDM9000LMCADN", "^NQDM9000LMEUR", "^NQDM9000LMEURN", "^NQDM9000LMGBP", "^NQDM9000LMGBPN", "^NQDM9000LMJPY", "^NQDM9000LMJPYN", "^NQDM9000LMN", "^NQDMXGBLM", "^NQDMXGBLMAUD", "^NQDMXGBLMAUDN", "^NQDMXGBLMCAD", "^NQDMXGBLMCADN", "^NQDMXGBLMEUR", "^NQDMXGBLMEURN", "^NQDMXGBLMGBP", "^NQDMXGBLMGBPN", "^NQDMXGBLMJPY", "^NQDMXGBLMJPYN", "^NQDMXGBLMN", "^NQDMXJPLM", "^NQDMXJPLMAUD", "^NQDMXJPLMAUDN", "^NQDMXJPLMCAD", "^NQDMXJPLMCADN", "^NQDMXJPLMEUR", "^NQDMXJPLMEURN", "^NQDMXJPLMGBP", "^NQDMXJPLMGBPN", "^NQDMXJPLMJPY", "^NQDMXJPLMJPYN", "^NQDMXJPLMN", "^NQDMXKR", "^NQDMXKRAUD", "^NQDMXKRAUDN", "^NQDMXKRCAD", "^NQDMXKRCADN", "^NQDMXKREUR", "^NQDMXKREURN", "^NQDMXKRGBP", "^NQDMXKRGBPN", "^NQDMXKRJPY", "^NQDMXKRJPYN", "^NQDMXKRLM", "^NQDMXKRLMAUD", "^NQDMXKRLMAUDN", "^NQDMXKRLMCAD", "^NQDMXKRLMCADN", "^NQDMXKRLMEUR", "^NQDMXKRLMEURN", "^NQDMXKRLMGBP", "^NQDMXKRLMGBPN", "^NQDMXKRLMJPY", "^NQDMXKRLMJPYN", "^NQDMXKRLMN", "^NQDMXKRMC", "^NQDMXKRMCAUD", "^NQDMXKRMCAUDN", "^NQDMXKRMCCAD", "^NQDMXKRMCCADN", "^NQDMXKRMCEUR", "^NQDMXKRMCEURN", "^NQDMXKRMCGBP", "^NQDMXKRMCGBPN", "^NQDMXKRMCJPY", "^NQDMXKRMCJPYN", "^NQDMXKRMCN", "^NQDMXKRN", "^NQDOG", "^NQDOGN", "^NQDXUSLC", "^NQDXUSLCEUR", "^NQDXUSLCEURN", "^NQDXUSLCG", "^NQDXUSLCGBP", "^NQDXUSLCGBPN", "^NQDXUSLCGN", "^NQDXUSLCN", "^NQDXUSMLTCG", "^NQDXUSMLTCGN", "^NQFFUSG", "^NQFFUSGN", "^NQFFUSM", "^NQFFUSMN", "^NQG9000LM", "^NQG9000LMAUD", "^NQG9000LMAUDN", "^NQG9000LMCAD", "^NQG9000LMCADN", "^NQG9000LMEUR", "^NQG9000LMEURN", "^NQG9000LMGBP", "^NQG9000LMGBPN", "^NQG9000LMJPY", "^NQG9000LMJPYN", "^NQG9000LMN", "^NQGXGBLM", "^NQGXGBLMAUD", "^NQGXGBLMAUDN", "^NQGXGBLMCAD", "^NQGXGBLMCADN", "^NQGXGBLMEUR", "^NQGXGBLMEURN", "^NQGXGBLMGBP", "^NQGXGBLMGBPN", "^NQGXGBLMJPY", "^NQGXGBLMJPYN", "^NQGXGBLMN", "^NQGXJPLM", "^NQGXJPLMAUD", "^NQGXJPLMAUDN", "^NQGXJPLMCAD", "^NQGXJPLMCADN", "^NQGXJPLMEUR", "^NQGXJPLMEURN", "^NQGXJPLMGBP", "^NQGXJPLMGBPN", "^NQGXJPLMJPY", "^NQGXJPLMJPYN", "^NQGXJPLMN", "^NQNA9000LM", "^NQNA9000LMAUD", "^NQNA9000LMAUDN", "^NQNA9000LMCAD", "^NQNA9000LMCADN", "^NQNA9000LMEUR", "^NQNA9000LMEURN", "^NQNA9000LMGBP", "^NQNA9000LMGBPN", "^NQNA9000LMJPY", "^NQNA9000LMJPYN", "^NQNA9000LMN", "^NQNALM", "^NQNALMAUD", "^NQNALMAUDN", "^NQNALMCAD", "^NQNALMCADN", "^NQNALMEUR", "^NQNALMEURN", "^NQNALMGBP", "^NQNALMGBPN", "^NQNALMJPY", "^NQNALMJPYN", "^NQNALMN", "^NQSSSE", "^NQSSSEN", "^NQUS500LC", "^NQUS500LCG", "^NQUS500LCGN", "^NQUS500LCN", "^NQUSB9000LM", "^NQUSB9000LMAUD", "^NQUSB9000LMAUDN", "^NQUSB9000LMCAD", "^NQUSB9000LMCADN", "^NQUSB9000LMEUR", "^NQUSB9000LMEURN", "^NQUSB9000LMGBP", "^NQUSB9000LMGBPN", "^NQUSB9000LMJPY", "^NQUSB9000LMJPYN", "^NQUSB9000LMN", "^NQUSB9570LM", "^NQUSB9570LMAUD", "^NQUSB9570LMAUDN", "^NQUSB9570LMCAD", "^NQUSB9570LMCADN", "^NQUSB9570LMEUR", "^NQUSB9570LMEURN", "^NQUSB9570LMGBP", "^NQUSB9570LMGBPN", "^NQUSB9570LMJPY", "^NQUSB9570LMJPYN", "^NQUSB9570LMN", "^NQUSB9576LM", "^NQUSB9576LMAUD", "^NQUSB9576LMAUDN", "^NQUSB9576LMCAD", "^NQUSB9576LMCADN", "^NQUSB9576LMEUR", "^NQUSB9576LMEURN", "^NQUSB9576LMGBP", "^NQUSB9576LMGBPN", "^NQUSB9576LMJPY", "^NQUSB9576LMJPYN", "^NQUSB9576LMN", "^NQUSBLM", "^NQUSBLMAUD", "^NQUSBLMAUDN", "^NQUSBLMCAD", "^NQUSBLMCADN", "^NQUSBLMEUR", "^NQUSBLMEURN", "^NQUSBLMGBP", "^NQUSBLMGBPN", "^NQUSBLMJPY", "^NQUSBLMJPYN", "^NQUSBLMN", "^NQUSMGN", "^NQUSMLTCG", "^NQUSMLTCGN", "^RCMP", "^SOX", "^SX", "^XCI", "^XCQ"], "hasPrePostMarketData": true, "firstTradeDateMilliseconds": 322151400000, "symbol": "AMD"}, {"language": "en-US", "region": "US", "quoteType": "EQUITY", "typeDisp": "Equity", "quoteSourceName": "Delayed Quote", "triggerable": true, "customPriceAlertConfidence": "HIGH", "quoteSummary": {"summaryDetail": {"maxAge": 1, "priceHint": 2, "previousClose": 266.91, "open": 267.99, "dayLow": 267.53, "dayHigh": 270.17, "regularMarketPreviousClose": 266.91, "regularMarketOpen": 267.99, "regularMarketDayLow": 267.53, "regularMarketDayHigh": 270.17, "dividendRate": 6.72, "dividendYield": 0.025, "exDividendDate": 1746748800, "payoutRatio": 1.1419001, "fiveYearAvgDividendYield": 4.46, "beta": 0.652, "trailingPE": 45.88225, "forwardPE": 25.341188, "volume": 1865615, "regularMarketVolume": 1865615, "averageVolume": 4612125, "averageVolume10days": 3335900, "averageDailyVolume10Day": 3335900, "bid": 268.57, "ask": 268.51, "bidSize": 300, "askSize": 100, "marketCap": 249886965760, "fiftyTwoWeekLow": 166.81, "fiftyTwoWeekHigh": 270.17, "priceToSalesTrailing12Months": 3.9770653, "fiftyDayAverage": 248.8944, "twoHundredDayAverage": 233.38405, "trailingAnnualDividendRate": 6.68, "trailingAnnualDividendYield": 0.025027161, "currency": "USD", "fromCurrency": null, "toCurrency": null, "lastMarket": null, "coinMarketCapLink": null, "algorithm": null, "tradeable": false}, "earnings": {"maxAge": 86400, "earningsChart": {"quarterly": [{"date": "2Q2024", "actual": 2.43, "estimate": 2.17118}, {"date": "3Q2024", "actual": 2.3, "estimate": 2.22596}, {"date": "4Q2024", "actual": 3.92, "estimate": 3.77627}, {"date": "1Q2025", "actual": 1.6, "estimate": 1.427}], "currentQuarterEstimate": 2.6465, "currentQuarterEstimateDate": "2Q", "currentQuarterEstimateYear": 2025, "earningsDate": [1753300800], "isEarningsDateEstimate": false}, "financialsChart": {"yearly": [{"date": 2021, "revenue": 57350000000, "earnings": 7174000000}, {"date": 2022, "revenue": 60530000000, "earnings": 8326000000}, {"date": 2023, "revenue": 61860000000, "earnings": 8870000000}, {"date": 2024, "revenue": 62753000000, "earnings": 9684000000}], "quarterly": [{"date": "2Q2024", "revenue": 15770000000, "earnings": 2275000000}, {"date": "3Q2024", "revenue": 14968000000, "earnings": 2155000000}, {"date": "4Q2024", "revenue": 17553000000, "earnings": 3690000000}, {"date": "1Q2025", "revenue": 14541000000, "earnings": 1517000000}]}, "financialCurrency": "USD"}}, "priceHint": 2, "totalCash": 17464999900, "floatShares": 927379784, "ebitda": 13950000128, "shortRatio": 4.43, "targetPriceHigh": 300, "targetPriceLow": 170, "targetPriceMean": 252.272, "targetPriceMedian": 259.995, "heldPercentInsiders": 0.12, "heldPercentInstitutions": 65.263, "postMarketChangePercent": 0.044629417, "postMarketTime": 1749254174, "postMarketPrice": 268.99, "postMarketChange": 0.11999512, "regularMarketChange": 2.01001, "regularMarketChangePercent": 0.753208, "regularMarketTime": 1749240002, "regularMarketPrice": 268.87, "regularMarketDayHigh": 270.17, "regularMarketDayRange": "267.53 - 270.17", "regularMarketDayLow": 267.53, "regularMarketVolume": 1865615, "sharesShort": 21305645, "sharesShortPrevMonth": 20988517, "shortPercentFloat": 2.55, "regularMarketPreviousClose": 266.91, "bid": 268.57, "ask": 268.51, "bidSize": 3, "askSize": 1, "exchange": "NYQ", "market": "us_market", "messageBoardId": "finmb_112350", "fullExchangeName": "NYSE", "shortName": "International Business Machines", "longName": "International Business Machines Corporation", "financialCurrency": "USD", "regularMarketOpen": 267.99, "averageDailyVolume3Month": 4612125, "averageDailyVolume10Day": 3335900, "beta": 0.652, "fiftyTwoWeekLowChange": 102.06, "fiftyTwoWeekLowChangePercent": 0.6118338, "fiftyTwoWeekRange": "166.81 - 270.17", "fiftyTwoWeekHighChange": -1.3000183, "fiftyTwoWeekHighChangePercent": -0.0048118527, "fiftyTwoWeekLow": 166.81, "fiftyTwoWeekHigh": 270.17, "dividendDate": 1749513600, "exDividendDate": 1746748800, "earningsTimestamp": 1753300800, "earningsTimestampStart": 1753300800, "earningsTimestampEnd": 1753300800, "trailingAnnualDividendRate": 6.68, "trailingPE": 45.88225, "dividendsPerShare": 6.68, "dividendRate": 6.72, "trailingAnnualDividendYield": 0.025027161, "dividendYield": 2.5, "revenue": ***********, "priceToSales": 3.9770653, "marketState": "CLOSED", "epsTrailingTwelveMonths": 5.86, "epsForward": 10.61, "epsCurrentYear": 10.90037, "epsNextQuarter": 2.42523, "priceEpsCurrentYear": 24.666136, "priceEpsNextQuarter": 110.86371, "sharesOutstanding": 929396992, "bookValue": 28.922, "fiftyDayAverage": 248.8944, "fiftyDayAverageChange": 19.975601, "fiftyDayAverageChangePercent": 0.080257334, "twoHundredDayAverage": 233.38405, "twoHundredDayAverageChange": 35.485947, "twoHundredDayAverageChangePercent": 0.15204959, "marketCap": 249886965760, "forwardPE": 25.341188, "priceToBook": 9.296383, "sourceInterval": 15, "exchangeDataDelayedBy": 0, "exchangeTimezoneName": "America/New_York", "exchangeTimezoneShortName": "EDT", "pageViews": {"midTermTrend": "UP", "longTermTrend": "UP", "shortTermTrend": "DOWN"}, "gmtOffSetMilliseconds": -14400000, "esgPopulated": false, "tradeable": false, "cryptoTradeable": false, "currency": "USD", "components": ["^AXI", "^BGD", "^BGDNTR", "^CPQ", "^CPQNTR", "^DAA", "^DAAXMLPREIT", "^DJA", "^DJI", "^IBMSY", "^MSH", "^NQ96DIVUS", "^NQCAPST", "^NQCRD", "^NQCRDN", "^NQDM9000LM", "^NQDM9000LMAUD", "^NQDM9000LMAUDN", "^NQDM9000LMCAD", "^NQDM9000LMCADN", "^NQDM9000LMEUR", "^NQDM9000LMEURN", "^NQDM9000LMGBP", "^NQDM9000LMGBPN", "^NQDM9000LMJPY", "^NQDM9000LMJPYN", "^NQDM9000LMN", "^NQDMXGBLM", "^NQDMXGBLMAUD", "^NQDMXGBLMAUDN", "^NQDMXGBLMCAD", "^NQDMXGBLMCADN", "^NQDMXGBLMEUR", "^NQDMXGBLMEURN", "^NQDMXGBLMGBP", "^NQDMXGBLMGBPN", "^NQDMXGBLMJPY", "^NQDMXGBLMJPYN", "^NQDMXGBLMN", "^NQDMXJPLM", "^NQDMXJPLMAUD", "^NQDMXJPLMAUDN", "^NQDMXJPLMCAD", "^NQDMXJPLMCADN", "^NQDMXJPLMEUR", "^NQDMXJPLMEURN", "^NQDMXJPLMGBP", "^NQDMXJPLMGBPN", "^NQDMXJPLMJPY", "^NQDMXJPLMJPYN", "^NQDMXJPLMN", "^NQDMXKR", "^NQDMXKRAUD", "^NQDMXKRAUDN", "^NQDMXKRCAD", "^NQDMXKRCADN", "^NQDMXKREUR", "^NQDMXKREURN", "^NQDMXKRGBP", "^NQDMXKRGBPN", "^NQDMXKRJPY", "^NQDMXKRJPYN", "^NQDMXKRLC", "^NQDMXKRLCAUD", "^NQDMXKRLCAUDN", "^NQDMXKRLCCAD", "^NQDMXKRLCCADN", "^NQDMXKRLCEUR", "^NQDMXKRLCEURN", "^NQDMXKRLCGBP", "^NQDMXKRLCGBPN", "^NQDMXKRLCJPY", "^NQDMXKRLCJPYN", "^NQDMXKRLCN", "^NQDMXKRLM", "^NQDMXKRLMAUD", "^NQDMXKRLMAUDN", "^NQDMXKRLMCAD", "^NQDMXKRLMCADN", "^NQDMXKRLMEUR", "^NQDMXKRLMEURN", "^NQDMXKRLMGBP", "^NQDMXKRLMGBPN", "^NQDMXKRLMJPY", "^NQDMXKRLMJPYN", "^NQDMXKRLMN", "^NQDMXKRN", "^NQDXUSLC", "^NQDXUSLCEUR", "^NQDXUSLCEURN", "^NQDXUSLCGBP", "^NQDXUSLCGBPN", "^NQDXUSLCN", "^NQDXUSLCV", "^NQDXUSLCVN", "^NQDXUSMEGA", "^NQDXUSMEGAN", "^NQDXUSMLTCV", "^NQDXUSMLTCVN", "^NQFFUSHY", "^NQFFUSHYN", "^NQFFUSLV", "^NQFFUSLVN", "^NQG9000LM", "^NQG9000LMAUD", "^NQG9000LMAUDN", "^NQG9000LMCAD", "^NQG9000LMCADN", "^NQG9000LMEUR", "^NQG9000LMEURN", "^NQG9000LMGBP", "^NQG9000LMGBPN", "^NQG9000LMJPY", "^NQG9000LMJPYN", "^NQG9000LMN", "^NQGIHEI", "^NQGIHEIEUR", "^NQGIHEIEURN", "^NQGIHEIGBP", "^NQGIHEIGBPN", "^NQGIHEIN", "^NQGMOI", "^NQGMOIN", "^NQGXGBLM", "^NQGXGBLMAUD", "^NQGXGBLMAUDN", "^NQGXGBLMCAD", "^NQGXGBLMCADN", "^NQGXGBLMEUR", "^NQGXGBLMEURN", "^NQGXGBLMGBP", "^NQGXGBLMGBPN", "^NQGXGBLMJPY", "^NQGXGBLMJPYN", "^NQGXGBLMN", "^NQGXJPLM", "^NQGXJPLMAUD", "^NQGXJPLMAUDN", "^NQGXJPLMCAD", "^NQGXJPLMCADN", "^NQGXJPLMEUR", "^NQGXJPLMEURN", "^NQGXJPLMGBP", "^NQGXJPLMGBPN", "^NQGXJPLMJPY", "^NQGXJPLMJPYN", "^NQGXJPLMN", "^NQNA9000LM", "^NQNA9000LMAUD", "^NQNA9000LMAUDN", "^NQNA9000LMCAD", "^NQNA9000LMCADN", "^NQNA9000LMEUR", "^NQNA9000LMEURN", "^NQNA9000LMGBP", "^NQNA9000LMGBPN", "^NQNA9000LMJPY", "^NQNA9000LMJPYN", "^NQNA9000LMN", "^NQNALM", "^NQNALMAUD", "^NQNALMAUDN", "^NQNALMCAD", "^NQNALMCADN", "^NQNALMEUR", "^NQNALMEURN", "^NQNALMGBP", "^NQNALMGBPN", "^NQNALMJPY", "^NQNALMJPYN", "^NQNALMN", "^NQSHYL", "^NQSHYLN", "^NQUS500LC", "^NQUS500LCN", "^NQUS500LCV", "^NQUS500LCVN", "^NQUSB9000LM", "^NQUSB9000LMAUD", "^NQUSB9000LMAUDN", "^NQUSB9000LMCAD", "^NQUSB9000LMCADN", "^NQUSB9000LMEUR", "^NQUSB9000LMEURN", "^NQUSB9000LMGBP", "^NQUSB9000LMGBPN", "^NQUSB9000LMJPY", "^NQUSB9000LMJPYN", "^NQUSB9000LMN", "^NQUSB9530LM", "^NQUSB9530LMAUD", "^NQUSB9530LMAUDN", "^NQUSB9530LMCAD", "^NQUSB9530LMCADN", "^NQUSB9530LMEUR", "^NQUSB9530LMEURN", "^NQUSB9530LMGBP", "^NQUSB9530LMGBPN", "^NQUSB9530LMJPY", "^NQUSB9530LMJPYN", "^NQUSB9530LMN", "^NQUSB9533LM", "^NQUSB9533LMAUD", "^NQUSB9533LMAUDN", "^NQUSB9533LMCAD", "^NQUSB9533LMCADN", "^NQUSB9533LMEUR", "^NQUSB9533LMEURN", "^NQUSB9533LMGBP", "^NQUSB9533LMGBPN", "^NQUSB9533LMJPY", "^NQUSB9533LMJPYN", "^NQUSB9533LMN", "^NQUSBLM", "^NQUSBLMAUD", "^NQUSBLMAUDN", "^NQUSBLMCAD", "^NQUSBLMCADN", "^NQUSBLMEUR", "^NQUSBLMEURN", "^NQUSBLMGBP", "^NQUSBLMGBPN", "^NQUSBLMJPY", "^NQUSBLMJPYN", "^NQUSBLMN", "^NQUSHEI", "^NQUSHEIEUR", "^NQUSHEIEURN", "^NQUSHEIGBP", "^NQUSHEIGBPN", "^NQUSHEIN", "^NQUSLV", "^NQUSLVN", "^NQUSMLTCV", "^NQUSMLTCVN", "^NQVMVUS", "^NQVMVUSN", "^QCRD", "^XCI", "^XMI"], "hasPrePostMarketData": true, "firstTradeDateMilliseconds": -252322200000, "symbol": "IBM"}, {"language": "en-US", "region": "US", "quoteType": "EQUITY", "typeDisp": "Equity", "quoteSourceName": "Delayed Quote", "triggerable": true, "customPriceAlertConfidence": "HIGH", "quoteSummary": {"summaryDetail": {"maxAge": 1, "priceHint": 2, "previousClose": 200.63, "open": 202.995, "dayLow": 202.05, "dayHigh": 205.7, "regularMarketPreviousClose": 200.63, "regularMarketOpen": 202.995, "regularMarketDayLow": 202.05, "regularMarketDayHigh": 205.7, "dividendRate": 1.04, "dividendYield": 0.0050999997, "exDividendDate": 1747008000, "payoutRatio": 0.1558, "fiveYearAvgDividendYield": 0.56, "beta": 1.211, "trailingPE": 31.763239, "forwardPE": 24.539108, "volume": 46121557, "regularMarketVolume": 46121557, "averageVolume": 61983674, "averageVolume10days": 52944710, "averageDailyVolume10Day": 52944710, "bid": 203.8, "ask": 204.17, "bidSize": 1400, "askSize": 500, "marketCap": 3045708267520, "fiftyTwoWeekLow": 169.21, "fiftyTwoWeekHigh": 260.1, "priceToSalesTrailing12Months": 7.60731, "fiftyDayAverage": 203.6794, "twoHundredDayAverage": 225.1178, "trailingAnnualDividendRate": 1, "trailingAnnualDividendYield": 0.004984299, "currency": "USD", "fromCurrency": null, "toCurrency": null, "lastMarket": null, "coinMarketCapLink": null, "algorithm": null, "tradeable": false}, "earnings": {"maxAge": 86400, "earningsChart": {"quarterly": [{"date": "2Q2024", "actual": 1.4, "estimate": 1.34155}, {"date": "3Q2024", "actual": 0.97, "estimate": 0.94654}, {"date": "4Q2024", "actual": 2.4, "estimate": 2.34102}, {"date": "1Q2025", "actual": 1.65, "estimate": 1.62253}], "currentQuarterEstimate": 1.41586, "currentQuarterEstimateDate": "2Q", "currentQuarterEstimateYear": 2025, "earningsDate": [1753873140, 1754308800], "isEarningsDateEstimate": true}, "financialsChart": {"yearly": [{"date": 2021, "revenue": 365817000000, "earnings": 94680000000}, {"date": 2022, "revenue": 394328000000, "earnings": 99803000000}, {"date": 2023, "revenue": 383285000000, "earnings": 96995000000}, {"date": 2024, "revenue": 391035000000, "earnings": 93736000000}], "quarterly": [{"date": "2Q2024", "revenue": 85777000000, "earnings": 21448000000}, {"date": "3Q2024", "revenue": 94930000000, "earnings": 14736000000}, {"date": "4Q2024", "revenue": 124300000000, "earnings": 36330000000}, {"date": "1Q2025", "revenue": 95359000000, "earnings": 24780000000}]}, "financialCurrency": "USD"}}, "priceHint": 2, "totalCash": 48498000000, "floatShares": 14911480604, "ebitda": 138865999872, "shortRatio": 1.97, "targetPriceHigh": 300, "targetPriceLow": 170.62, "targetPriceMean": 228.85326, "targetPriceMedian": 232.5, "heldPercentInsiders": 2.085, "heldPercentInstitutions": 62.898, "postMarketChangePercent": 0.27952498, "postMarketTime": 1749254394, "postMarketPrice": 204.49, "postMarketChange": 0.5700073, "regularMarketChange": 3.28999, "regularMarketChangePercent": 1.63983, "regularMarketTime": 1749240001, "regularMarketPrice": 203.92, "regularMarketDayHigh": 205.7, "regularMarketDayRange": "202.05 - 205.7", "regularMarketDayLow": 202.05, "regularMarketVolume": 46121557, "sharesShort": 105169332, "sharesShortPrevMonth": 113127198, "shortPercentFloat": 0.7, "regularMarketPreviousClose": 200.63, "bid": 203.8, "ask": 204.17, "bidSize": 14, "askSize": 5, "exchange": "NMS", "market": "us_market", "messageBoardId": "finmb_24937", "fullExchangeName": "NasdaqGS", "shortName": "Apple Inc.", "longName": "Apple Inc.", "financialCurrency": "USD", "regularMarketOpen": 202.995, "averageDailyVolume3Month": 61983674, "averageDailyVolume10Day": 52944710, "beta": 1.211, "fiftyTwoWeekLowChange": 34.70999, "fiftyTwoWeekLowChangePercent": 0.20512967, "fiftyTwoWeekRange": "169.21 - 260.1", "fiftyTwoWeekHighChange": -56.180008, "fiftyTwoWeekHighChangePercent": -0.21599388, "fiftyTwoWeekLow": 169.21, "fiftyTwoWeekHigh": 260.1, "dividendDate": 1747267200, "exDividendDate": 1747008000, "earningsTimestamp": 1746131400, "earningsTimestampStart": 1753873140, "earningsTimestampEnd": 1754308800, "trailingAnnualDividendRate": 1, "trailingPE": 31.763239, "dividendsPerShare": 1, "dividendRate": 1.04, "trailingAnnualDividendYield": 0.004984299, "dividendYield": 0.51, "revenue": 400366010000, "priceToSales": 7.60731, "marketState": "CLOSED", "epsTrailingTwelveMonths": 6.42, "epsForward": 8.31, "epsCurrentYear": 7.18956, "epsNextQuarter": 1.65979, "priceEpsCurrentYear": 28.36335, "priceEpsNextQuarter": 122.85891, "sharesOutstanding": 14935799808, "bookValue": 4.471, "fiftyDayAverage": 203.6794, "fiftyDayAverageChange": 0.24060059, "fiftyDayAverageChangePercent": 0.001181271, "twoHundredDayAverage": 225.1178, "twoHundredDayAverageChange": -21.1978, "twoHundredDayAverageChangePercent": -0.09416314, "marketCap": 3045708267520, "forwardPE": 24.539108, "priceToBook": 45.60948, "sourceInterval": 15, "exchangeDataDelayedBy": 0, "exchangeTimezoneName": "America/New_York", "exchangeTimezoneShortName": "EDT", "pageViews": {"midTermTrend": "UP", "longTermTrend": "UP", "shortTermTrend": "UP"}, "gmtOffSetMilliseconds": -14400000, "esgPopulated": false, "tradeable": false, "cryptoTradeable": false, "currency": "USD", "components": ["^AIX", "^AVSPY", "^CAPEXA", "^CAPEXAN", "^CPQ", "^CPQNTR", "^DJA", "^DJI", "^DWANQTL", "^DWARJFMU", "^DWUSSR", "^GMB", "^GMBNTR", "^IXCO", "^IXIC", "^MNX1", "^MSH", "^NDX", "^NDXCAD", "^NDXCHF", "^NDXE", "^NDXEUR", "^NDXGBP", "^NDXHKD", "^NDXT", "^NQ96DIVUS", "^NQBUY", "^NQCAPST", "^NQCRD", "^NQCRDN", "^NQDM9000LM", "^NQDM9000LMAUD", "^NQDM9000LMAUDN", "^NQDM9000LMCAD", "^NQDM9000LMCADN", "^NQDM9000LMEUR", "^NQDM9000LMEURN", "^NQDM9000LMGBP", "^NQDM9000LMGBPN", "^NQDM9000LMJPY", "^NQDM9000LMJPYN", "^NQDM9000LMN", "^NQDMXGBLM", "^NQDMXGBLMAUD", "^NQDMXGBLMAUDN", "^NQDMXGBLMCAD", "^NQDMXGBLMCADN", "^NQDMXGBLMEUR", "^NQDMXGBLMEURN", "^NQDMXGBLMGBP", "^NQDMXGBLMGBPN", "^NQDMXGBLMJPY", "^NQDMXGBLMJPYN", "^NQDMXGBLMN", "^NQDMXJPLM", "^NQDMXJPLMAUD", "^NQDMXJPLMAUDN", "^NQDMXJPLMCAD", "^NQDMXJPLMCADN", "^NQDMXJPLMEUR", "^NQDMXJPLMEURN", "^NQDMXJPLMGBP", "^NQDMXJPLMGBPN", "^NQDMXJPLMJPY", "^NQDMXJPLMJPYN", "^NQDMXJPLMN", "^NQDMXKR", "^NQDMXKRAUD", "^NQDMXKRAUDN", "^NQDMXKRCAD", "^NQDMXKRCADN", "^NQDMXKREUR", "^NQDMXKREURN", "^NQDMXKRGBP", "^NQDMXKRGBPN", "^NQDMXKRJPY", "^NQDMXKRJPYN", "^NQDMXKRLC", "^NQDMXKRLCAUD", "^NQDMXKRLCAUDN", "^NQDMXKRLCCAD", "^NQDMXKRLCCADN", "^NQDMXKRLCEUR", "^NQDMXKRLCEURN", "^NQDMXKRLCGBP", "^NQDMXKRLCGBPN", "^NQDMXKRLCJPY", "^NQDMXKRLCJPYN", "^NQDMXKRLCN", "^NQDMXKRLM", "^NQDMXKRLMAUD", "^NQDMXKRLMAUDN", "^NQDMXKRLMCAD", "^NQDMXKRLMCADN", "^NQDMXKRLMEUR", "^NQDMXKRLMEURN", "^NQDMXKRLMGBP", "^NQDMXKRLMGBPN", "^NQDMXKRLMJPY", "^NQDMXKRLMJPYN", "^NQDMXKRLMN", "^NQDMXKRN", "^NQDOG", "^NQDOGN", "^NQDVRIS", "^NQDXUSLC", "^NQDXUSLCEUR", "^NQDXUSLCEURN", "^NQDXUSLCG", "^NQDXUSLCGBP", "^NQDXUSLCGBPN", "^NQDXUSLCGN", "^NQDXUSLCN", "^NQDXUSMEGA", "^NQDXUSMEGAN", "^NQDXUSMLTCG", "^NQDXUSMLTCGN", "^NQFFUSM", "^NQFFUSMN", "^NQFGSL", "^NQFGSLN", "^NQG9000LM", "^NQG9000LMAUD", "^NQG9000LMAUDN", "^NQG9000LMCAD", "^NQG9000LMCADN", "^NQG9000LMEUR", "^NQG9000LMEURN", "^NQG9000LMGBP", "^NQG9000LMGBPN", "^NQG9000LMJPY", "^NQG9000LMJPYN", "^NQG9000LMN", "^NQGMOI", "^NQGMOIN", "^NQGS", "^NQGXGBLM", "^NQGXGBLMAUD", "^NQGXGBLMAUDN", "^NQGXGBLMCAD", "^NQGXGBLMCADN", "^NQGXGBLMEUR", "^NQGXGBLMEURN", "^NQGXGBLMGBP", "^NQGXGBLMGBPN", "^NQGXGBLMJPY", "^NQGXGBLMJPYN", "^NQGXGBLMN", "^NQGXJPLM", "^NQGXJPLMAUD", "^NQGXJPLMAUDN", "^NQGXJPLMCAD", "^NQGXJPLMCADN", "^NQGXJPLMEUR", "^NQGXJPLMEURN", "^NQGXJPLMGBP", "^NQGXJPLMGBPN", "^NQGXJPLMJPY", "^NQGXJPLMJPYN", "^NQGXJPLMN", "^NQNA9000LM", "^NQNA9000LMAUD", "^NQNA9000LMAUDN", "^NQNA9000LMCAD", "^NQNA9000LMCADN", "^NQNA9000LMEUR", "^NQNA9000LMEURN", "^NQNA9000LMGBP", "^NQNA9000LMGBPN", "^NQNA9000LMJPY", "^NQNA9000LMJPYN", "^NQNA9000LMN", "^NQNALM", "^NQNALMAUD", "^NQNALMAUDN", "^NQNALMCAD", "^NQNALMCADN", "^NQNALMEUR", "^NQNALMEURN", "^NQNALMGBP", "^NQNALMGBPN", "^NQNALMJPY", "^NQNALMJPYN", "^NQNALMN", "^NQSHYL", "^NQSHYLN", "^NQSXY", "^NQSXYN", "^NQUS500LC", "^NQUS500LCG", "^NQUS500LCGN", "^NQUS500LCN", "^NQUSB9000LM", "^NQUSB9000LMAUD", "^NQUSB9000LMAUDN", "^NQUSB9000LMCAD", "^NQUSB9000LMCADN", "^NQUSB9000LMEUR", "^NQUSB9000LMEURN", "^NQUSB9000LMGBP", "^NQUSB9000LMGBPN", "^NQUSB9000LMJPY", "^NQUSB9000LMJPYN", "^NQUSB9000LMN", "^NQUSB9570LM", "^NQUSB9570LMAUD", "^NQUSB9570LMAUDN", "^NQUSB9570LMCAD", "^NQUSB9570LMCADN", "^NQUSB9570LMEUR", "^NQUSB9570LMEURN", "^NQUSB9570LMGBP", "^NQUSB9570LMGBPN", "^NQUSB9570LMJPY", "^NQUSB9570LMJPYN", "^NQUSB9570LMN", "^NQUSB9572LM", "^NQUSB9572LMAUD", "^NQUSB9572LMAUDN", "^NQUSB9572LMCAD", "^NQUSB9572LMCADN", "^NQUSB9572LMEUR", "^NQUSB9572LMEURN", "^NQUSB9572LMGBP", "^NQUSB9572LMGBPN", "^NQUSB9572LMJPY", "^NQUSB9572LMJPYN", "^NQUSB9572LMN", "^NQUSBLM", "^NQUSBLMAUD", "^NQUSBLMAUDN", "^NQUSBLMCAD", "^NQUSBLMCADN", "^NQUSBLMEUR", "^NQUSBLMEURN", "^NQUSBLMGBP", "^NQUSBLMGBPN", "^NQUSBLMJPY", "^NQUSBLMJPYN", "^NQUSBLMN", "^NQUSLG", "^NQUSLGN", "^NQUSMLTCG", "^NQUSMLTCGN", "^NQVMVUS", "^NQVMVUSN", "^QCRD", "^QFON", "^QIV", "^QMI", "^XCI", "^XCQ", "^XNDXNNR", "^XNDXNNRCAD", "^XNDXNNRCHF", "^XNDXNNREUR", "^XNDXNNRGBP", "^XNDXNNRHKD", "^XQC", "^XQO"], "hasPrePostMarketData": true, "firstTradeDateMilliseconds": 345479400000, "symbol": "AAPL"}], "error": null}}