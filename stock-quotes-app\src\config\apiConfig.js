// Multi-API Configuration using Environment Variables
// This approach keeps your API keys secure and out of source control

// API Keys from environment variables
export const API_KEYS = {
  ALPHA_VANTAGE: import.meta.env.VITE_ALPHA_VANTAGE_API_KEY,
  FINNHUB: import.meta.env.VITE_FINNHUB_API_KEY,
  TWELVE_DATA: import.meta.env.VITE_TWELVE_DATA_API_KEY,
  RAPIDAPI: import.meta.env.VITE_RAPIDAPI_KEY
};

// API Provider configurations
export const API_PROVIDERS = {
  ALPHA_VANTAGE: {
    name: 'Alpha Vantage',
    key: 'ALPHA_VANTAGE',
    description: '25 calls/day free',
    baseUrl: 'https://www.alphavantage.co/query',
    signupUrl: 'https://www.alphavantage.co/support/#api-key'
  },
  FINNHUB: {
    name: 'Finnhub',
    key: 'FINNHUB',
    description: '60 calls/minute free',
    baseUrl: 'https://finnhub.io/api/v1',
    signupUrl: 'https://finnhub.io/register'
  },
  TWELVE_DATA: {
    name: 'Twelve Data',
    key: 'TWELVE_DATA',
    description: '800 calls/day free',
    baseUrl: 'https://api.twelvedata.com',
    signupUrl: 'https://twelvedata.com/pricing'
  },
  RAPIDAPI: {
    name: 'Yahoo Finance (RapidAPI)',
    key: 'RAPIDAPI',
    description: '500 calls/month free',
    baseUrl: 'https://apidojo-yahoo-finance-v1.p.rapidapi.com',
    signupUrl: 'https://rapidapi.com/apidojo/api/yahoo-finance1/'
  }
};

// Get available API providers (those with configured keys)
export const getAvailableProviders = () => {
  return Object.values(API_PROVIDERS).filter(provider => {
    const key = API_KEYS[provider.key];
    return key && key !== `your-${provider.key.toLowerCase().replace('_', '-')}-api-key-here`;
  });
};

// Get the default API provider (first available one)
export const getDefaultProvider = () => {
  const available = getAvailableProviders();
  return available.length > 0 ? available[0].key : null;
};

// Validate API configuration
const availableProviders = getAvailableProviders();
if (availableProviders.length === 0) {
  console.warn('⚠️ No API providers configured!');
  console.warn('Please configure at least one API key in your .env file');
  console.warn('Available providers:', Object.keys(API_PROVIDERS));
} else {
  console.log('✅ Available API providers:', availableProviders.map(p => p.name));
}

// Legacy export for backward compatibility
export const ALPHA_VANTAGE_API_KEY = API_KEYS.ALPHA_VANTAGE;
