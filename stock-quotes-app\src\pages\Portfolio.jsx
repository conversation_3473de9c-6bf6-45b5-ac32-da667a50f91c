import { useState } from 'react';
import { useApi } from '../context/ApiContext';
import PortfolioBuilder from '../components/PortfolioBuilder';
import PortfolioResults from '../components/PortfolioResults';
import { runPortfolioBacktest } from '../services/multiApiService';

const Portfolio = () => {
  const { selectedProvider } = useApi();
  const [results, setResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleRunBacktest = async (portfolioData) => {
    setIsLoading(true);
    setError(null);
    setResults(null);

    try {
      console.log('Starting backtest with data:', portfolioData);

      // Check if API provider is selected
      if (!selectedProvider) {
        throw new Error('No API provider selected. Please configure at least one API key.');
      }

      const backtestResults = await runPortfolioBacktest(portfolioData, selectedProvider);
      setResults(backtestResults);
      console.log('Backtest completed successfully');
    } catch (err) {
      console.error('Backtest error:', err);

      let errorMessage = 'An error occurred while running the backtest.';

      if (err.message.includes('API key')) {
        errorMessage = 'API key not configured properly. Please check your .env file.';
      } else if (err.message.includes('rate limit') || err.message.includes('429')) {
        errorMessage = 'API rate limit exceeded. Please wait a moment and try again.';
      } else if (err.message.includes('Unable to get data')) {
        errorMessage = `${err.message}. Please check the stock symbol and try again.`;
      } else {
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="page">
      {!selectedProvider && (
        <div className="no-provider-warning">
          <h3>⚠️ No API Provider Selected</h3>
          <p>Please go to the <strong>Configuration tab</strong> (first tab) to select an API provider.</p>
        </div>
      )}

      <PortfolioBuilder
        onRunBacktest={handleRunBacktest}
        isLoading={isLoading}
        disabled={!selectedProvider}
      />

      <PortfolioResults
        results={results}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
};

export default Portfolio;
