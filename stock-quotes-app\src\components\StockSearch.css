.stock-search {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.stock-search h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.8rem;
}

.search-form {
  margin-bottom: 1rem;
}

.input-group {
  display: flex;
  gap: 0.5rem;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.search-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.search-button {
  padding: 0.75rem 1.5rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-button:hover:not(:disabled) {
  background-color: #2980b9;
}

.search-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.search-examples {
  text-align: center;
  margin-top: 1rem;
}

.search-examples p {
  color: #7f8c8d;
  margin-bottom: 0.5rem;
}

.example-button {
  background: none;
  border: 1px solid #3498db;
  color: #3498db;
  padding: 0.25rem 0.75rem;
  margin: 0 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.example-button:hover:not(:disabled) {
  background-color: #3498db;
  color: white;
}

.example-button:disabled {
  border-color: #bdc3c7;
  color: #bdc3c7;
  cursor: not-allowed;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .stock-search {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .stock-search h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .input-group {
    flex-direction: column;
    max-width: 100%;
    gap: 0.75rem;
  }

  .search-input {
    width: 100%;
    font-size: 1.1rem; /* Slightly larger for mobile */
    padding: 1rem; /* More padding for easier touch */
  }

  .search-button {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
  }

  .search-examples {
    margin-top: 1.5rem;
  }

  .example-button {
    display: block;
    width: 100%;
    margin: 0.5rem 0;
    padding: 0.75rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .stock-search {
    padding: 0.75rem;
    border-radius: 8px;
  }

  .stock-search h2 {
    font-size: 1.3rem;
  }

  .search-input, .search-button {
    font-size: 1rem;
    padding: 0.875rem;
  }
}
