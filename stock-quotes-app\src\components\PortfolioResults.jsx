import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import { isTradingDay, formatDateLabel } from '../utils/tradingCalendar';
import './PortfolioResults.css';

const PortfolioResults = ({ results, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="portfolio-results loading">
        <div className="loading-spinner"></div>
        <p>Running backtest analysis...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-results error">
        <h3>Backtest Error</h3>
        <p>{error}</p>
      </div>
    );
  }

  if (!results) {
    return null;
  }

  const {
    portfolio,
    totalInvestment,
    currentValue,
    totalGainLoss,
    totalGainLossPercent,
    holdings,
    startDate,
    endDate,
    country = 'US',
    market = 'NYSE/NASDAQ'
  } = results;

  const isPositive = totalGainLoss >= 0;
  const gainLossClass = isPositive ? 'positive' : 'negative';
  const gainLossSymbol = isPositive ? '+' : '';

  // Prepare data for charts
  const pieData = holdings.map(holding => ({
    name: holding.symbol,
    value: holding.currentValue,
    allocation: holding.allocation
  }));

  const barData = holdings.map(holding => ({
    symbol: holding.symbol,
    invested: holding.investedAmount,
    current: holding.currentValue,
    gainLoss: holding.gainLoss
  }));

  const COLORS = ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6'];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatPercent = (percent) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  return (
    <div className="portfolio-results">
      <div className="results-header">
        <h2>📊 Portfolio Backtest Results</h2>
        <div className="date-range">
          <span className="start-date">
            {formatDateLabel(startDate)} ({startDate})
          </span>
          <span className="arrow">→</span>
          <span className="end-date">
            {formatDateLabel(endDate)} ({endDate})
          </span>
        </div>
        <div className="trading-day-info">
          {isTradingDay(startDate, country) ? '✅' : '⚠️'} Start date is {isTradingDay(startDate, country) ? 'a valid trading day' : 'not a trading day'}
        </div>
        <div className="market-info">
          🏛️ Market: {market} ({country === 'UK' ? '🇬🇧' : '🇺🇸'} {country})
        </div>
      </div>

      <div className="summary-cards">
        <div className="summary-card">
          <h3>Initial Investment</h3>
          <div className="amount">{formatCurrency(totalInvestment)}</div>
        </div>
        <div className="summary-card">
          <h3>Current Value</h3>
          <div className="amount">{formatCurrency(currentValue)}</div>
        </div>
        <div className="summary-card">
          <h3>Total Gain/Loss</h3>
          <div className={`amount ${gainLossClass}`}>
            {gainLossSymbol}{formatCurrency(Math.abs(totalGainLoss))}
          </div>
          <div className={`percentage ${gainLossClass}`}>
            ({formatPercent(totalGainLossPercent)})
          </div>
        </div>
      </div>

      <div className="charts-section">
        <div className="chart-container">
          <h3>Portfolio Allocation</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, allocation }) => `${name} (${allocation}%)`}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatCurrency(value)} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="chart-container">
          <h3>Performance by Stock</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={barData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="symbol" />
              <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
              <Tooltip formatter={(value) => formatCurrency(value)} />
              <Bar dataKey="invested" fill="#bdc3c7" name="Invested" />
              <Bar dataKey="current" fill="#3498db" name="Current Value" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="holdings-table">
        <h3>Detailed Holdings</h3>
        <div className="table-container">
          <table>
            <thead>
              <tr>
                <th>Stock</th>
                <th>Allocation</th>
                <th>Shares</th>
                <th>Start Price</th>
                <th>Current Price</th>
                <th>Invested</th>
                <th>Current Value</th>
                <th>Gain/Loss</th>
                <th>Return %</th>
              </tr>
            </thead>
            <tbody>
              {holdings.map((holding, index) => (
                <tr key={index}>
                  <td className="symbol">{holding.symbol}</td>
                  <td>{holding.allocation}%</td>
                  <td>{holding.shares.toFixed(2)}</td>
                  <td>{formatCurrency(holding.startPrice)}</td>
                  <td>{formatCurrency(holding.currentPrice)}</td>
                  <td>{formatCurrency(holding.investedAmount)}</td>
                  <td>{formatCurrency(holding.currentValue)}</td>
                  <td className={holding.gainLoss >= 0 ? 'positive' : 'negative'}>
                    {formatCurrency(holding.gainLoss)}
                  </td>
                  <td className={holding.returnPercent >= 0 ? 'positive' : 'negative'}>
                    {formatPercent(holding.returnPercent)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PortfolioResults;
