.trading-date-picker {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.date-input-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.date-input:focus {
  outline: none;
  border-color: #3498db;
}

.date-input.invalid {
  border-color: #e74c3c;
  background-color: #fdf2f2;
}

.quick-select-button {
  padding: 0.75rem;
  background: #f8f9fa;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.quick-select-button:hover {
  background: #e9ecef;
  border-color: #3498db;
}

.date-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.date-status.trading {
  background-color: #d5f4e6;
  color: #27ae60;
  border: 1px solid #a8e6cf;
}

.date-status.weekend {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.date-status.holiday {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-icon {
  font-size: 1rem;
}

.validation-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #fdf2f2;
  border: 1px solid #e74c3c;
  border-radius: 6px;
  font-size: 0.9rem;
}

.error-icon {
  color: #e74c3c;
  font-size: 1rem;
}

.error-text {
  color: #721c24;
  flex: 1;
}

.suggestion-button {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.suggestion-button:hover {
  background: #c0392b;
}

.quick-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 0.5rem;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.dropdown-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.date-options {
  max-height: 300px;
  overflow-y: auto;
}

.date-option {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  background: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid #f8f9fa;
}

.date-option:hover {
  background: #f8f9fa;
}

.date-option:last-child {
  border-bottom: none;
}

.option-label {
  font-weight: 600;
  color: #2c3e50;
}

.option-date {
  color: #7f8c8d;
  font-size: 0.9rem;
  font-family: monospace;
}

.dropdown-footer {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e1e8ed;
  border-radius: 0 0 8px 8px;
  text-align: center;
}

.dropdown-footer small {
  color: #7f8c8d;
  font-style: italic;
}

@media (max-width: 768px) {
  .quick-select-dropdown {
    position: fixed;
    top: 50%;
    left: 1rem;
    right: 1rem;
    transform: translateY(-50%);
    max-height: 70vh;
  }
  
  .date-options {
    max-height: 50vh;
  }
}
