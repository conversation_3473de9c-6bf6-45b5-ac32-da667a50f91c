import './ApiProviderCard.css';

const ApiProviderCard = ({ provider, isSelected, isAvailable, onSelect }) => {
  const handleSelect = () => {
    if (isAvailable) {
      onSelect();
    }
  };

  return (
    <div className={`api-provider-card ${isSelected ? 'selected' : ''} ${!isAvailable ? 'disabled' : ''}`}>
      <div className="card-header">
        <h3>{provider.name}</h3>
        <div className="card-badges">
          {isSelected && <span className="badge selected-badge">✅ Selected</span>}
          {isAvailable ? (
            <span className="badge available-badge">🟢 Available</span>
          ) : (
            <span className="badge unavailable-badge">🔴 Not Configured</span>
          )}
        </div>
      </div>
      
      <div className="card-content">
        <p className="description">{provider.description}</p>
        
        <div className="card-actions">
          {isAvailable ? (
            <button 
              className={`select-button ${isSelected ? 'selected' : ''}`}
              onClick={handleSelect}
              disabled={isSelected}
            >
              {isSelected ? 'Currently Selected' : 'Select Provider'}
            </button>
          ) : (
            <div className="setup-actions">
              <p className="setup-message">API key not configured</p>
              <a 
                href={provider.signupUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="signup-button"
              >
                Get API Key →
              </a>
            </div>
          )}
        </div>
      </div>
      
      <div className="card-footer">
        <small>Base URL: {provider.baseUrl}</small>
      </div>
    </div>
  );
};

export default ApiProviderCard;
