import { useState } from 'react';
import { useApi } from '../context/ApiContext';
import StockSearch from '../components/StockSearch';
import StockQuote from '../components/StockQuote';
import StockChart from '../components/StockChart';
import { getStockQuote, getDailyStockData } from '../services/multiApiService';

const StockQuotes = () => {
  const { selectedProvider } = useApi();
  const [quote, setQuote] = useState(null);
  const [chartData, setChartData] = useState(null);
  const [isLoadingQuote, setIsLoadingQuote] = useState(false);
  const [isLoadingChart, setIsLoadingChart] = useState(false);
  const [error, setError] = useState(null);
  const [currentSymbol, setCurrentSymbol] = useState('');

  const handleSearch = async (symbol) => {
    setCurrentSymbol(symbol);
    setError(null);
    setIsLoadingQuote(true);
    setIsLoadingChart(true);

    try {
      // Fetch both quote and chart data simultaneously
      const [quoteData, dailyData] = await Promise.all([
        getStockQuote(symbol, selectedProvider),
        getDailyStockData(symbol, selectedProvider)
      ]);

      if (quoteData) {
        setQuote(quoteData);
      } else {
        setError(`No quote data found for symbol "${symbol}". Please check the ticker symbol.`);
      }

      if (dailyData) {
        setChartData(dailyData);
      } else {
        setError(prev => prev || `No chart data found for symbol "${symbol}".`);
      }

    } catch (err) {
      setError('An error occurred while fetching stock data. Please try again.');
      console.error('Error fetching stock data:', err);
    } finally {
      setIsLoadingQuote(false);
      setIsLoadingChart(false);
    }
  };

  return (
    <div className="page">
      {!selectedProvider && (
        <div className="no-provider-warning">
          <h3>⚠️ No API Provider Selected</h3>
          <p>Please go to the <strong>Configuration tab</strong> (first tab) to select an API provider.</p>
        </div>
      )}

      <StockSearch
        onSearch={handleSearch}
        isLoading={isLoadingQuote || isLoadingChart}
        disabled={!selectedProvider}
      />

      <StockQuote
        quote={quote}
        isLoading={isLoadingQuote}
        error={error}
      />

      <StockChart
        data={chartData}
        symbol={currentSymbol}
        isLoading={isLoadingChart}
        error={error}
      />
    </div>
  );
};

export default StockQuotes;
