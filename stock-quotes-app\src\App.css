* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
  color: #2c3e50;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

.app-footer {
  background-color: #34495e;
  color: white;
  text-align: center;
  padding: 1.5rem;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .app-header {
    padding: 2rem 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-main {
    padding: 1rem;
  }
}

.page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.no-provider-warning {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.no-provider-warning h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.no-provider-warning p {
  margin: 0;
  opacity: 0.9;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .app {
    min-height: 100vh;
  }

  .app-header {
    padding: 1.5rem 1rem;
    text-align: center;
  }

  .app-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .app-header p {
    font-size: 1rem;
    margin: 0;
  }

  .app-main {
    padding: 1rem;
  }

  .page {
    max-width: 100%;
    padding: 0;
  }

  .no-provider-warning {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .no-provider-warning h3 {
    font-size: 1.1rem;
  }

  .app-footer {
    padding: 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 1rem 0.75rem;
  }

  .app-header h1 {
    font-size: 1.75rem;
  }

  .app-header p {
    font-size: 0.9rem;
  }

  .app-main {
    padding: 0.75rem;
  }

  .no-provider-warning {
    padding: 0.75rem;
  }

  .app-footer {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
}
