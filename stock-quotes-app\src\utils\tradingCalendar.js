/**
 * Trading Calendar Utilities
 * Handles US stock market holidays and trading days
 */

// Market Holidays by Country
// US Stock Market Holidays (NYSE/NASDAQ)
const US_MARKET_HOLIDAYS = {
  2024: [
    '2024-01-01', // New Year's Day
    '2024-01-15', // <PERSON> Jr. Day
    '2024-02-19', // Presidents' Day
    '2024-03-29', // Good Friday
    '2024-05-27', // Memorial Day
    '2024-06-19', // Juneteenth
    '2024-07-04', // Independence Day
    '2024-09-02', // Labor Day
    '2024-11-28', // Thanksgiving
    '2024-12-25', // Christmas Day
  ],
  2023: [
    '2023-01-02', // New Year's Day (observed)
    '2023-01-16', // <PERSON> Jr. Day
    '2023-02-20', // Presidents' Day
    '2023-04-07', // Good Friday
    '2023-05-29', // Memorial Day
    '2023-06-19', // Juneteenth
    '2023-07-04', // Independence Day
    '2023-09-04', // Labor Day
    '2023-11-23', // Thanksgiving
    '2023-12-25', // Christmas Day
  ],
  2025: [
    '2025-01-01', // New Year's Day
    '2025-01-20', // <PERSON> <PERSON> Jr. Day
    '2025-02-17', // Presidents' Day
    '2025-04-18', // Good Friday
    '2025-05-26', // Memorial Day
    '2025-06-19', // Juneteenth
    '2025-07-04', // Independence Day
    '2025-09-01', // Labor Day
    '2025-11-27', // Thanksgiving
    '2025-12-25', // Christmas Day
  ]
};

// UK Stock Market Holidays (LSE - London Stock Exchange)
const UK_MARKET_HOLIDAYS = {
  2024: [
    '2024-01-01', // New Year's Day
    '2024-03-29', // Good Friday
    '2024-04-01', // Easter Monday
    '2024-05-06', // Early May Bank Holiday
    '2024-05-27', // Spring Bank Holiday
    '2024-08-26', // Summer Bank Holiday
    '2024-12-25', // Christmas Day
    '2024-12-26', // Boxing Day
  ],
  2023: [
    '2023-01-02', // New Year's Day (observed)
    '2023-04-07', // Good Friday
    '2023-04-10', // Easter Monday
    '2023-05-01', // Early May Bank Holiday
    '2023-05-08', // Coronation Bank Holiday
    '2023-05-29', // Spring Bank Holiday
    '2023-08-28', // Summer Bank Holiday
    '2023-12-25', // Christmas Day
    '2023-12-26', // Boxing Day
  ],
  2025: [
    '2025-01-01', // New Year's Day
    '2025-04-18', // Good Friday
    '2025-04-21', // Easter Monday
    '2025-05-05', // Early May Bank Holiday
    '2025-05-26', // Spring Bank Holiday
    '2025-08-25', // Summer Bank Holiday
    '2025-12-25', // Christmas Day
    '2025-12-26', // Boxing Day
  ]
};

// Combined holidays object
const MARKET_HOLIDAYS = {
  US: US_MARKET_HOLIDAYS,
  UK: UK_MARKET_HOLIDAYS
};

/**
 * Check if a date is a weekend (Saturday or Sunday)
 */
export const isWeekend = (date) => {
  const day = new Date(date).getDay();
  return day === 0 || day === 6; // Sunday = 0, Saturday = 6
};

/**
 * Check if a date is a market holiday for the specified country
 */
export const isMarketHoliday = (dateString, country = 'US') => {
  const year = new Date(dateString).getFullYear();
  const holidays = MARKET_HOLIDAYS[country]?.[year] || [];
  return holidays.includes(dateString);
};

/**
 * Check if a date is a trading day (not weekend or holiday)
 */
export const isTradingDay = (dateString, country = 'US') => {
  return !isWeekend(dateString) && !isMarketHoliday(dateString, country);
};

/**
 * Get the previous trading day from a given date
 */
export const getPreviousTradingDay = (dateString, country = 'US') => {
  let currentDate = new Date(dateString);

  // Go back one day at a time until we find a trading day
  do {
    currentDate.setDate(currentDate.getDate() - 1);
    const dateStr = currentDate.toISOString().split('T')[0];

    if (isTradingDay(dateStr, country)) {
      return dateStr;
    }
  } while (true);
};

/**
 * Get the next trading day from a given date
 */
export const getNextTradingDay = (dateString, country = 'US') => {
  let currentDate = new Date(dateString);

  // Go forward one day at a time until we find a trading day
  do {
    currentDate.setDate(currentDate.getDate() + 1);
    const dateStr = currentDate.toISOString().split('T')[0];

    if (isTradingDay(dateStr, country)) {
      return dateStr;
    }
  } while (true);
};

/**
 * Get the closest trading day (prefer previous, but go next if needed)
 */
export const getClosestTradingDay = (dateString, country = 'US') => {
  if (isTradingDay(dateString, country)) {
    return dateString;
  }

  // Try to find a previous trading day first (more common for backtesting)
  try {
    return getPreviousTradingDay(dateString, country);
  } catch {
    // If no previous trading day found, get next one
    return getNextTradingDay(dateString, country);
  }
};

/**
 * Validate if a date is suitable for backtesting
 */
export const validateBacktestDate = (dateString) => {
  const date = new Date(dateString);
  const today = new Date();

  // Set today to start of day for comparison
  today.setHours(0, 0, 0, 0);
  date.setHours(0, 0, 0, 0);

  // Check if date is in the future
  if (date >= today) {
    return {
      valid: false,
      reason: 'Backtest date cannot be today or in the future',
      suggestion: getPreviousTradingDay(new Date(today.getTime() - 24 * 60 * 60 * 1000).toISOString().split('T')[0])
    };
  }
  
  // Check if it's a trading day
  if (!isTradingDay(dateString)) {
    const reason = isWeekend(dateString) ? 'Weekends are not trading days' : 'This date is a market holiday';
    return {
      valid: false,
      reason,
      suggestion: getPreviousTradingDay(dateString)
    };
  }
  
  return { valid: true };
};

/**
 * Get a list of recent trading days for quick selection
 */
export const getRecentTradingDays = (count = 10) => {
  const tradingDays = [];
  let currentDate = new Date();
  
  // Start from yesterday (today's data might not be complete)
  currentDate.setDate(currentDate.getDate() - 1);
  
  while (tradingDays.length < count) {
    const dateStr = currentDate.toISOString().split('T')[0];
    
    if (isTradingDay(dateStr)) {
      tradingDays.push({
        date: dateStr,
        label: formatDateLabel(dateStr)
      });
    }
    
    currentDate.setDate(currentDate.getDate() - 1);
  }
  
  return tradingDays;
};

/**
 * Format date for display labels
 */
export const formatDateLabel = (dateString) => {
  const date = new Date(dateString);
  const options = {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };
  return date.toLocaleDateString('en-US', options);
};

/**
 * Get common backtest start dates
 */
export const getCommonBacktestDates = () => {
  const today = new Date();
  const currentYear = today.getFullYear();

  const commonDates = [];

  // Only add current year start if we're past January 2nd
  const currentYearStart = `${currentYear}-01-02`;
  if (new Date(currentYearStart) < today) {
    commonDates.push({
      date: getClosestTradingDay(currentYearStart),
      label: `Start of ${currentYear}`
    });
  }

  // Start of previous year (always safe)
  commonDates.push({
    date: getClosestTradingDay(`${currentYear - 1}-01-02`),
    label: `Start of ${currentYear - 1}`
  });
  // 6 months ago
  const sixMonthsAgo = new Date(today);
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
  commonDates.push({
    date: getClosestTradingDay(sixMonthsAgo.toISOString().split('T')[0]),
    label: '6 months ago'
  });

  // 3 months ago
  const threeMonthsAgo = new Date(today);
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
  commonDates.push({
    date: getClosestTradingDay(threeMonthsAgo.toISOString().split('T')[0]),
    label: '3 months ago'
  });

  // 1 month ago
  const oneMonthAgo = new Date(today);
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  commonDates.push({
    date: getClosestTradingDay(oneMonthAgo.toISOString().split('T')[0]),
    label: '1 month ago'
  });

  return commonDates.filter(item => item.date && new Date(item.date) < today); // Remove any invalid or future dates
};
