Portfolio.jsx:19 Starting backtest with data: {investmentAmount: 10000, startDate: '2024-01-02', stocks: Array(1)}
multiApiService.js:591 Starting optimized portfolio backtest using Yahoo Finance (RapidAPI)
multiApiService.js:592 Backtest period: 2024-01-02 to 2025-06-07
multiApiService.js:598 Processing AAPL (1/1)
multiApiService.js:602 📊 Fetching current quote for AAPL
multiApiService.js:381 Fetching quote for AAPL using Yahoo Finance (RapidAPI)
multiApiService.js:205 Yahoo Finance API Response: {quoteResponse: {…}}
multiApiService.js:609 📅 Fetching historical price for AAPL on 2024-01-02
multiApiService.js:441 Getting historical price for AAPL on 2024-01-02 using RAPIDAPI
multiApiService.js:511 Making optimized API call for AAPL around 2024-01-02
multiApiService.js:512 Epoch range: 1703548800 to 1704758400
multiApiService.js:528 Optimized API response for AAPL: {timeseries: {…}}
multiApiService.js:571 No price data found for AAPL around 2024-01-02
getHistoricalPriceOptimized @ multiApiService.js:571
await in getHistoricalPriceOptimized
getHistoricalPrice @ multiApiService.js:445
runPortfolioBacktest @ multiApiService.js:610
await in runPortfolioBacktest
handleRunBacktest @ Portfolio.jsx:26
handleSubmit @ PortfolioBuilder.jsx:64
executeDispatch @ react-dom_client.js?v=cd1a1ee0:11736
runWithFiberInDEV @ react-dom_client.js?v=cd1a1ee0:1485
processDispatchQueue @ react-dom_client.js?v=cd1a1ee0:11772
(anonymous) @ react-dom_client.js?v=cd1a1ee0:12182
batchedUpdates$1 @ react-dom_client.js?v=cd1a1ee0:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=cd1a1ee0:11877
dispatchEvent @ react-dom_client.js?v=cd1a1ee0:14792
dispatchDiscreteEvent @ react-dom_client.js?v=cd1a1ee0:14773
multiApiService.js:643 ❌ Error processing AAPL: Error: Unable to get historical price for AAPL on 2024-01-02
    at runPortfolioBacktest (multiApiService.js:613:17)
    at async handleRunBacktest (Portfolio.jsx:26:31)
runPortfolioBacktest @ multiApiService.js:643
await in runPortfolioBacktest
handleRunBacktest @ Portfolio.jsx:26
handleSubmit @ PortfolioBuilder.jsx:64
executeDispatch @ react-dom_client.js?v=cd1a1ee0:11736
runWithFiberInDEV @ react-dom_client.js?v=cd1a1ee0:1485
processDispatchQueue @ react-dom_client.js?v=cd1a1ee0:11772
(anonymous) @ react-dom_client.js?v=cd1a1ee0:12182
batchedUpdates$1 @ react-dom_client.js?v=cd1a1ee0:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=cd1a1ee0:11877
dispatchEvent @ react-dom_client.js?v=cd1a1ee0:14792
dispatchDiscreteEvent @ react-dom_client.js?v=cd1a1ee0:14773
multiApiService.js:667 Error running portfolio backtest: Error: Failed to get data for AAPL: Unable to get historical price for AAPL on 2024-01-02
    at runPortfolioBacktest (multiApiService.js:644:15)
    at async handleRunBacktest (Portfolio.jsx:26:31)
runPortfolioBacktest @ multiApiService.js:667
await in runPortfolioBacktest
handleRunBacktest @ Portfolio.jsx:26
handleSubmit @ PortfolioBuilder.jsx:64
executeDispatch @ react-dom_client.js?v=cd1a1ee0:11736
runWithFiberInDEV @ react-dom_client.js?v=cd1a1ee0:1485
processDispatchQueue @ react-dom_client.js?v=cd1a1ee0:11772
(anonymous) @ react-dom_client.js?v=cd1a1ee0:12182
batchedUpdates$1 @ react-dom_client.js?v=cd1a1ee0:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=cd1a1ee0:11877
dispatchEvent @ react-dom_client.js?v=cd1a1ee0:14792
dispatchDiscreteEvent @ react-dom_client.js?v=cd1a1ee0:14773
Portfolio.jsx:30 Backtest error: Error: Failed to get data for AAPL: Unable to get historical price for AAPL on 2024-01-02
    at runPortfolioBacktest (multiApiService.js:644:15)
    at async handleRunBacktest (Portfolio.jsx:26:31)
handleRunBacktest @ Portfolio.jsx:30
await in handleRunBacktest
handleSubmit @ PortfolioBuilder.jsx:64
executeDispatch @ react-dom_client.js?v=cd1a1ee0:11736
runWithFiberInDEV @ react-dom_client.js?v=cd1a1ee0:1485
processDispatchQueue @ react-dom_client.js?v=cd1a1ee0:11772
(anonymous) @ react-dom_client.js?v=cd1a1ee0:12182
batchedUpdates$1 @ react-dom_client.js?v=cd1a1ee0:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=cd1a1ee0:11877
dispatchEvent @ react-dom_client.js?v=cd1a1ee0:14792
dispatchDiscreteEvent @ react-dom_client.js?v=cd1a1ee0:14773
