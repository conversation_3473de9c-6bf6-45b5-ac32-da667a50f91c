import { Link, useLocation } from 'react-router-dom';
import './Navigation.css';

const Navigation = () => {
  const location = useLocation();

  return (
    <nav className="navigation">
      <div className="nav-container">
        <Link
          to="/"
          className={`nav-link ${location.pathname === '/' || location.pathname === '/config' ? 'active' : ''}`}
        >
          ⚙️ Configuration
        </Link>
        <Link
          to="/quotes"
          className={`nav-link ${location.pathname === '/quotes' ? 'active' : ''}`}
        >
          📈 Stock Quotes
        </Link>
        <Link
          to="/portfolio"
          className={`nav-link ${location.pathname === '/portfolio' ? 'active' : ''}`}
        >
          📊 Portfolio Backtest
        </Link>
      </div>
    </nav>
  );
};

export default Navigation;
