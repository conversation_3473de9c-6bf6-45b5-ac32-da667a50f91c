import './StockQuote.css';

const StockQuote = ({ quote, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="stock-quote loading">
        <div className="loading-spinner"></div>
        <p>Loading stock data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="stock-quote error">
        <h3>Error</h3>
        <p>{error}</p>
      </div>
    );
  }

  if (!quote) {
    return null;
  }

  const isPositive = quote.change >= 0;
  const changeClass = isPositive ? 'positive' : 'negative';
  const changeSymbol = isPositive ? '+' : '';

  return (
    <div className="stock-quote">
      <div className="quote-header">
        <h2 className="symbol">{quote.symbol}</h2>
        <div className="price-info">
          <span className="current-price">${quote.price.toFixed(2)}</span>
          <span className={`change ${changeClass}`}>
            {changeSymbol}${quote.change.toFixed(2)} ({quote.changePercent})
          </span>
        </div>
      </div>
      
      <div className="quote-details">
        <div className="detail-grid">
          <div className="detail-item">
            <span className="label">Open:</span>
            <span className="value">${quote.open.toFixed(2)}</span>
          </div>
          <div className="detail-item">
            <span className="label">High:</span>
            <span className="value">${quote.high.toFixed(2)}</span>
          </div>
          <div className="detail-item">
            <span className="label">Low:</span>
            <span className="value">${quote.low.toFixed(2)}</span>
          </div>
          <div className="detail-item">
            <span className="label">Previous Close:</span>
            <span className="value">${quote.previousClose.toFixed(2)}</span>
          </div>
          <div className="detail-item">
            <span className="label">Volume:</span>
            <span className="value">{quote.volume.toLocaleString()}</span>
          </div>
          <div className="detail-item">
            <span className="label">Latest Trading Day:</span>
            <span className="value">{quote.latestTradingDay}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockQuote;
