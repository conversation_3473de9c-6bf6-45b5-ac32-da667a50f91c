import { useState } from 'react';
import './StockSearch.css';

const StockSearch = ({ onSearch, isLoading }) => {
  const [symbol, setSymbol] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (symbol.trim()) {
      onSearch(symbol.trim().toUpperCase());
    }
  };

  const handleInputChange = (e) => {
    setSymbol(e.target.value);
  };

  return (
    <div className="stock-search">
      <h2>Stock Quote Search</h2>
      <form onSubmit={handleSubmit} className="search-form">
        <div className="input-group">
          <input
            type="text"
            value={symbol}
            onChange={handleInputChange}
            placeholder="Enter stock symbol (e.g., AAPL, GOOGL, MSFT)"
            className="search-input"
            disabled={isLoading}
          />
          <button 
            type="submit" 
            className="search-button"
            disabled={isLoading || !symbol.trim()}
          >
            {isLoading ? 'Searching...' : 'Get Quote'}
          </button>
        </div>
      </form>
      <div className="search-examples">
        <p>Popular stocks: 
          <button 
            type="button" 
            onClick={() => setSymbol('AAPL')}
            className="example-button"
            disabled={isLoading}
          >
            AAPL
          </button>
          <button 
            type="button" 
            onClick={() => setSymbol('GOOGL')}
            className="example-button"
            disabled={isLoading}
          >
            GOOGL
          </button>
          <button 
            type="button" 
            onClick={() => setSymbol('MSFT')}
            className="example-button"
            disabled={isLoading}
          >
            MSFT
          </button>
          <button 
            type="button" 
            onClick={() => setSymbol('TSLA')}
            className="example-button"
            disabled={isLoading}
          >
            TSLA
          </button>
        </p>
      </div>
    </div>
  );
};

export default StockSearch;
