{"name": "mcp-finance-server", "version": "1.0.0", "description": "MCP Server for Stock Market Data - Educational Example", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node src/test.js"}, "keywords": ["mcp", "model-context-protocol", "finance", "stocks", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "dotenv": "^16.3.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}}