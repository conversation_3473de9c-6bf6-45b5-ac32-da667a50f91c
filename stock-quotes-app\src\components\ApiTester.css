.api-tester {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
  border: 1px solid #e1e8ed;
}

.tester-info {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border-left: 4px solid #3498db;
}

.tester-info h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.tester-info p {
  margin: 0;
  color: #7f8c8d;
}

.tester-info .warning {
  color: #e67e22;
  font-weight: 600;
}

.tester-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: end;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.control-input {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.control-input:focus {
  outline: none;
  border-color: #3498db;
}

.test-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.test-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
  transform: translateY(-1px);
}

.test-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.test-results {
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.test-results.success {
  background: #d5f4e6;
  border: 1px solid #27ae60;
}

.test-results.error {
  background: #fdeaea;
  border: 1px solid #e74c3c;
}

.results-header {
  margin-bottom: 1rem;
}

.results-header h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.results-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.results-meta span {
  background: rgba(255, 255, 255, 0.7);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.results-content h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.quote-data {
  background: white;
  border-radius: 6px;
  padding: 1rem;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-row:last-child {
  border-bottom: none;
}

.data-row .label {
  font-weight: 600;
  color: #7f8c8d;
}

.data-row .value {
  font-weight: bold;
  color: #2c3e50;
}

.data-row .value.positive {
  color: #27ae60;
}

.data-row .value.negative {
  color: #e74c3c;
}

.historical-data {
  background: white;
  border-radius: 6px;
  padding: 1rem;
}

.data-summary {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.data-summary p {
  margin: 0.25rem 0;
  color: #7f8c8d;
}

.data-sample h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.sample-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
  font-size: 0.9rem;
}

.sample-row:last-child {
  border-bottom: none;
}

.sample-row .date {
  font-weight: 600;
  color: #2c3e50;
}

.sample-row .price {
  color: #3498db;
  font-weight: 600;
}

.sample-row .volume {
  color: #7f8c8d;
}

.error-content {
  background: white;
  border-radius: 6px;
  padding: 1rem;
}

.error-message {
  color: #e74c3c;
  font-weight: 600;
  margin: 0;
  padding: 0.5rem;
  background: #fff5f5;
  border-radius: 4px;
  border-left: 4px solid #e74c3c;
}

.no-providers {
  text-align: center;
  padding: 2rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
}

.no-providers h3 {
  margin: 0 0 1rem 0;
}

.no-providers p {
  margin: 0;
}

@media (max-width: 768px) {
  .tester-controls {
    grid-template-columns: 1fr;
  }
  
  .results-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .data-row, .sample-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
