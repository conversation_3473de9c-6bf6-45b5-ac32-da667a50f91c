import requests
import pandas as pd

def get_stock_quote(symbol, api_key):
    """
    Fetch the stock price for the given symbol using Alpha Vantage API.

    Args:
        symbol (str): The stock ticker symbol (e.g., AAPL for Apple).
        api_key (str): Your Alpha Vantage API key.

    Returns:
        float: The current stock price or None if an error occurs.
    """
    # Alpha Vantage API endpoint for stock quote data
    url = "https://www.alphavantage.co/query"
    
    # API parameters
    params = {
        "function": "GLOBAL_QUOTE",
        "symbol": symbol,
        "apikey": api_key
    }
    
    try:
        # Make the API request
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise an error for bad responses
        
        # Parse the JSON data
        data = response.json()
        if "Global Quote" in data:
            price = float(data["Global Quote"]["05. price"])
            return price
        else:
            print(f"Error: No data found for symbol '{symbol}'. Please check the ticker symbol and try again.")
            return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


def get_daily_stock_data(symbol, api_key):
    """
    Fetch the daily stock data for the given symbol using Alpha Vantage API and load it into a pandas DataFrame.

    Args:
        symbol (str): The stock ticker symbol (e.g., AAPL for Apple).
        api_key (str): Your Alpha Vantage API key.

    Returns:
        DataFrame: A pandas DataFrame containing the daily stock data or None if an error occurs.
    """
    # Alpha Vantage API endpoint for daily time series data
    url = "https://www.alphavantage.co/query"
    
    # API parameters
    params = {
        "function": "TIME_SERIES_DAILY",
        "symbol": symbol,
        "apikey": api_key,
        "outputsize": "compact"  # Use "compact" for the last 100 data points, full for all
    }
    
    try:
        # Make the API request
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise an error for bad responses
        
        # Parse the JSON data
        data = response.json()
        if "Time Series (Daily)" in data:
            # Convert the time series data to a pandas DataFrame
            df = pd.DataFrame.from_dict(data["Time Series (Daily)"], orient="index")
            df = df.rename(columns={
                "1. open": "Open",
                "2. high": "High",
                "3. low": "Low",
                "4. close": "Close",
                "5. volume": "Volume"
            })
            df.index = pd.to_datetime(df.index)
            df = df.astype(float)
            return df
        else:
            print(f"Error: No data found for symbol '{symbol}'. Please check the ticker symbol and try again.")
            return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


# Example usage
if __name__ == "__main__":
    with open("C:/Dev/APIKeys/AlphaVantage.txt", "r") as file:
        api_key = file.read().strip()
    stock_symbol = input("Enter the stock ticker symbol (e.g., AAPL): ")


    # Fetch and print the current stock price
    # price = get_stock_quote(stock_symbol, api_key)
    # if price is not None:
    #     print(f"The current price of {stock_symbol} is ${price:.2f}")
    # else:
    #     print("Failed to retrieve stock price.")


    df = get_daily_stock_data(stock_symbol, api_key)
    if df is not None:
        print(df.head(10))
    else:
        print("Failed to retrieve stock data.")