import { createContext, useContext, useState, useEffect } from 'react';
import { getDefaultProvider, getAvailableProviders } from '../config/apiConfig';

const ApiContext = createContext();

export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

export const ApiProvider = ({ children }) => {
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [availableProviders, setAvailableProviders] = useState([]);

  useEffect(() => {
    // Initialize available providers and default selection
    const providers = getAvailableProviders();
    setAvailableProviders(providers);
    
    // Load saved provider from localStorage or use default
    const savedProvider = localStorage.getItem('selectedApiProvider');
    if (savedProvider && providers.some(p => p.key === savedProvider)) {
      setSelectedProvider(savedProvider);
    } else {
      const defaultProvider = getDefaultProvider();
      setSelectedProvider(defaultProvider);
    }
  }, []);

  const changeProvider = (providerKey) => {
    setSelectedProvider(providerKey);
    // Save to localStorage for persistence
    localStorage.setItem('selectedApiProvider', providerKey);
  };

  const value = {
    selectedProvider,
    availableProviders,
    changeProvider
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};
