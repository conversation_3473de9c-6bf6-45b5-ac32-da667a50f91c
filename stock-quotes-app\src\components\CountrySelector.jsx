import './CountrySelector.css';

const CountrySelector = ({ value, onChange, label = "Market" }) => {
  const countries = [
    { code: 'US', name: 'United States', flag: '🇺🇸', exchange: 'NYSE/NASDAQ' },
    { code: 'UK', name: 'United Kingdom', flag: '🇬🇧', exchange: 'LSE' }
  ];

  return (
    <div className="country-selector">
      <label htmlFor="country-select" className="country-label">
        {label}
      </label>
      
      <select
        id="country-select"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="country-select"
      >
        {countries.map(country => (
          <option key={country.code} value={country.code}>
            {country.flag} {country.name} ({country.exchange})
          </option>
        ))}
      </select>
      
      <div className="country-info">
        {countries.find(c => c.code === value) && (
          <small>
            Trading on {countries.find(c => c.code === value).exchange}
          </small>
        )}
      </div>
    </div>
  );
};

export default CountrySelector;
