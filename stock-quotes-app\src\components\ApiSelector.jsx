import { useState, useEffect } from 'react';
import { API_PROVIDERS, getAvailableProviders, getDefaultProvider } from '../config/apiConfig';
import './ApiSelector.css';

const ApiSelector = ({ selectedProvider, onProviderChange }) => {
  const [availableProviders, setAvailableProviders] = useState([]);

  useEffect(() => {
    const providers = getAvailableProviders();
    setAvailableProviders(providers);
    
    // Set default provider if none selected
    if (!selectedProvider && providers.length > 0) {
      onProviderChange(providers[0].key);
    }
  }, [selectedProvider, onProviderChange]);

  const handleProviderChange = (event) => {
    onProviderChange(event.target.value);
  };

  if (availableProviders.length === 0) {
    return (
      <div className="api-selector error">
        <h3>⚠️ No API Providers Configured</h3>
        <p>Please configure at least one API key in your .env file:</p>
        <ul>
          {Object.values(API_PROVIDERS).map(provider => (
            <li key={provider.key}>
              <strong>{provider.name}</strong> ({provider.description}) - 
              <a href={provider.signupUrl} target="_blank" rel="noopener noreferrer">
                Get API Key
              </a>
            </li>
          ))}
        </ul>
        <p>After adding API keys, restart the development server.</p>
      </div>
    );
  }

  return (
    <div className="api-selector">
      <div className="selector-header">
        <label htmlFor="api-provider">📡 API Provider:</label>
        <select 
          id="api-provider"
          value={selectedProvider || ''}
          onChange={handleProviderChange}
          className="provider-select"
        >
          {availableProviders.map(provider => (
            <option key={provider.key} value={provider.key}>
              {provider.name} ({provider.description})
            </option>
          ))}
        </select>
      </div>
      
      {selectedProvider && (
        <div className="provider-info">
          <span className="provider-status">
            ✅ Using {API_PROVIDERS[selectedProvider]?.name}
          </span>
          <a 
            href={API_PROVIDERS[selectedProvider]?.signupUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="provider-link"
          >
            Get API Key
          </a>
        </div>
      )}
    </div>
  );
};

export default ApiSelector;
