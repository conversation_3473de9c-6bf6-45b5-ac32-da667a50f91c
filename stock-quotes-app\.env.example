# Stock API Configuration
# Copy this file to .env and replace with your actual API keys
# You only need to configure the APIs you want to use

# Alpha Vantage (25 calls/day free)
# Get key from: https://www.alphavantage.co/support/#api-key
VITE_ALPHA_VANTAGE_API_KEY=your-alpha-vantage-api-key-here

# Finnhub (60 calls/minute free)
# Get key from: https://finnhub.io/register
VITE_FINNHUB_API_KEY=your-finnhub-api-key-here

# Twelve Data (800 calls/day free)
# Get key from: https://twelvedata.com/pricing
VITE_TWELVE_DATA_API_KEY=your-twelve-data-api-key-here

# Yahoo Finance via RapidAPI (500 calls/month free)
# Get key from: https://rapidapi.com/apidojo/api/yahoo-finance1/
VITE_RAPIDAPI_KEY=your-rapidapi-key-here

# Instructions:
# 1. Get API keys from the providers above (you can start with just one)
# 2. Copy this file: cp .env.example .env
# 3. Replace the placeholder values with your real API keys
# 4. Restart the dev server: npm run dev
