// Simple test to check if the API is working
import { getStockQuote } from './src/services/stockApi.js';

// Read API key from .env file (for testing purposes)
const API_KEY = '88RBTO9JNUZ377GD'; // Your actual API key

async function testAPI() {
  console.log('Testing Alpha Vantage API...');
  
  try {
    const quote = await getStockQuote('AAPL', API_KEY);
    console.log('API Test Result:', quote);
    
    if (quote) {
      console.log('✅ API is working!');
      console.log(`AAPL Price: $${quote.price}`);
    } else {
      console.log('❌ API returned null');
    }
  } catch (error) {
    console.error('❌ API Test Failed:', error);
  }
}

testAPI();
