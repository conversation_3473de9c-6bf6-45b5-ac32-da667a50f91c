import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import './StockChart.css';

const StockChart = ({ data, symbol, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="stock-chart loading">
        <div className="loading-spinner"></div>
        <p>Loading chart data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="stock-chart error">
        <h3>Chart Error</h3>
        <p>{error}</p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return null;
  }

  // Format data for the chart (reverse to show oldest to newest)
  const chartData = data.slice().reverse().map(item => ({
    date: new Date(item.date).toLocaleDateString(),
    close: item.close,
    open: item.open,
    high: item.high,
    low: item.low,
    volume: item.volume
  }));

  const formatTooltip = (value, name) => {
    if (name === 'close') {
      return [`$${value.toFixed(2)}`, 'Close Price'];
    }
    return [value, name];
  };

  return (
    <div className="stock-chart">
      <div className="chart-header">
        <h3>{symbol} - Daily Price Chart (Last 100 Days)</h3>
      </div>
      
      <div className="chart-container">
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="date" 
              stroke="#7f8c8d"
              fontSize={12}
              interval="preserveStartEnd"
            />
            <YAxis 
              stroke="#7f8c8d"
              fontSize={12}
              domain={['dataMin - 5', 'dataMax + 5']}
              tickFormatter={(value) => `$${value.toFixed(0)}`}
            />
            <Tooltip 
              formatter={formatTooltip}
              labelStyle={{ color: '#2c3e50' }}
              contentStyle={{ 
                backgroundColor: 'white', 
                border: '1px solid #ddd',
                borderRadius: '8px'
              }}
            />
            <Line 
              type="monotone" 
              dataKey="close" 
              stroke="#3498db" 
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4, fill: '#3498db' }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      <div className="chart-info">
        <p>Showing closing prices for the last {data.length} trading days</p>
      </div>
    </div>
  );
};

export default StockChart;
