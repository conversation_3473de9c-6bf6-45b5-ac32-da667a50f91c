/**
 * Finance API Module
 * 
 * This module contains the core finance functionality extracted from the React app.
 * It provides methods to:
 * 1. Get current stock quotes
 * 2. Fetch historical stock data
 * 3. Run portfolio backtesting
 * 
 * This is the same logic from your React app, adapted for use in an MCP server.
 */

import axios from 'axios';
import { config } from './config.js';

export class FinanceAPI {
  constructor() {
    this.providers = {
      ALPHA_VANTAGE: {
        name: 'Alpha Vantage',
        baseUrl: 'https://www.alphavantage.co/query',
        key: 'ALPHA_VANTAGE'
      },
      RAPIDAPI: {
        name: 'Yahoo Finance (RapidAPI)',
        baseUrl: 'https://apidojo-yahoo-finance-v1.p.rapidapi.com',
        key: 'RAPIDAPI'
      }
    };
  }

  /**
   * Get current stock quote
   */
  async getStockQuote(symbol, provider = 'ALPHA_VANTAGE') {
    console.error(`[API] Getting quote for ${symbol} using ${provider}`);
    
    if (provider === 'ALPHA_VANTAGE') {
      return await this.getAlphaVantageQuote(symbol);
    } else if (provider === 'RAPIDAPI') {
      return await this.getYahooFinanceQuote(symbol);
    } else {
      throw new Error(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Alpha Vantage quote implementation
   */
  async getAlphaVantageQuote(symbol) {
    const apiKey = config.apiKeys.ALPHA_VANTAGE;
    if (!apiKey || apiKey.includes('your-')) {
      throw new Error('Alpha Vantage API key not configured');
    }

    const response = await axios.get(this.providers.ALPHA_VANTAGE.baseUrl, {
      params: {
        function: 'GLOBAL_QUOTE',
        symbol: symbol,
        apikey: apiKey
      }
    });

    const data = response.data;
    
    if (data['Error Message']) {
      throw new Error(`Alpha Vantage error: ${data['Error Message']}`);
    }

    if (data['Note']) {
      throw new Error('Alpha Vantage rate limit exceeded');
    }

    const quote = data['Global Quote'];
    if (!quote) {
      throw new Error(`No data found for symbol ${symbol}`);
    }

    return {
      symbol: quote['01. symbol'],
      price: parseFloat(quote['05. price']),
      change: parseFloat(quote['09. change']),
      changePercent: quote['10. change percent'],
      volume: parseInt(quote['06. volume'])
    };
  }

  /**
   * Yahoo Finance (RapidAPI) quote implementation
   */
  async getYahooFinanceQuote(symbol) {
    const apiKey = config.apiKeys.RAPIDAPI;
    if (!apiKey || apiKey.includes('your-')) {
      throw new Error('RapidAPI key not configured');
    }

    const response = await axios.get(`${this.providers.RAPIDAPI.baseUrl}/market/v2/get-quotes`, {
      params: {
        symbols: symbol,
        region: 'US'
      },
      headers: {
        'X-RapidAPI-Key': apiKey,
        'X-RapidAPI-Host': 'apidojo-yahoo-finance-v1.p.rapidapi.com'
      }
    });

    const data = response.data;
    
    if (!data.quoteResponse || !data.quoteResponse.result || data.quoteResponse.result.length === 0) {
      throw new Error(`No data found for symbol ${symbol}`);
    }

    const quote = data.quoteResponse.result[0];
    
    return {
      symbol: quote.symbol,
      price: quote.regularMarketPrice,
      change: quote.regularMarketChange,
      changePercent: `${quote.regularMarketChangePercent?.toFixed(2)}%`,
      volume: quote.regularMarketVolume
    };
  }

  /**
   * Get historical stock data
   */
  async getDailyStockData(symbol, provider = 'ALPHA_VANTAGE') {
    console.error(`[API] Getting historical data for ${symbol} using ${provider}`);
    
    if (provider === 'ALPHA_VANTAGE') {
      return await this.getAlphaVantageHistory(symbol);
    } else if (provider === 'RAPIDAPI') {
      return await this.getYahooFinanceHistory(symbol);
    } else {
      throw new Error(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Alpha Vantage historical data
   */
  async getAlphaVantageHistory(symbol) {
    const apiKey = config.apiKeys.ALPHA_VANTAGE;
    if (!apiKey || apiKey.includes('your-')) {
      throw new Error('Alpha Vantage API key not configured');
    }

    const response = await axios.get(this.providers.ALPHA_VANTAGE.baseUrl, {
      params: {
        function: 'TIME_SERIES_DAILY',
        symbol: symbol,
        apikey: apiKey,
        outputsize: 'compact' // Last 100 days
      }
    });

    const data = response.data;
    
    if (data['Error Message']) {
      throw new Error(`Alpha Vantage error: ${data['Error Message']}`);
    }

    if (data['Note']) {
      throw new Error('Alpha Vantage rate limit exceeded');
    }

    const timeSeries = data['Time Series (Daily)'];
    if (!timeSeries) {
      throw new Error(`No historical data found for symbol ${symbol}`);
    }

    // Convert to array format
    const dailyData = Object.entries(timeSeries).map(([date, values]) => ({
      date,
      open: parseFloat(values['1. open']),
      high: parseFloat(values['2. high']),
      low: parseFloat(values['3. low']),
      close: parseFloat(values['4. close']),
      volume: parseInt(values['5. volume'])
    }));

    // Sort by date (newest first)
    return dailyData.sort((a, b) => new Date(b.date) - new Date(a.date));
  }

  /**
   * Yahoo Finance historical data (simplified version)
   */
  async getYahooFinanceHistory(symbol) {
    // For this example, we'll use the chart endpoint
    const apiKey = config.apiKeys.RAPIDAPI;
    if (!apiKey || apiKey.includes('your-')) {
      throw new Error('RapidAPI key not configured');
    }

    const response = await axios.get(`${this.providers.RAPIDAPI.baseUrl}/stock/v2/get-chart`, {
      params: {
        interval: '1d',
        symbol: symbol,
        range: '3mo',
        region: 'US'
      },
      headers: {
        'X-RapidAPI-Key': apiKey,
        'X-RapidAPI-Host': 'apidojo-yahoo-finance-v1.p.rapidapi.com'
      }
    });

    const data = response.data;
    
    if (!data.chart?.result?.[0]) {
      throw new Error(`No historical data found for symbol ${symbol}`);
    }

    const result = data.chart.result[0];
    const timestamps = result.timestamp;
    const quote = result.indicators?.quote?.[0];
    
    if (!timestamps || !quote) {
      throw new Error(`Invalid data format for symbol ${symbol}`);
    }

    const dailyData = timestamps.map((timestamp, index) => ({
      date: new Date(timestamp * 1000).toISOString().split('T')[0],
      open: quote.open[index],
      high: quote.high[index],
      low: quote.low[index],
      close: quote.close[index],
      volume: quote.volume[index]
    })).filter(item => item.close !== null && item.close !== undefined);
    
    return dailyData.sort((a, b) => new Date(b.date) - new Date(a.date));
  }

  /**
   * Get historical price for a specific date
   */
  async getHistoricalPrice(symbol, targetDate, provider = 'ALPHA_VANTAGE') {
    const dailyData = await this.getDailyStockData(symbol, provider);
    
    if (!dailyData || dailyData.length === 0) {
      return null;
    }

    const targetDateTime = new Date(targetDate).getTime();
    const sortedData = dailyData.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    let closestPrice = null;
    for (const dataPoint of sortedData) {
      const dataPointTime = new Date(dataPoint.date).getTime();
      if (dataPointTime <= targetDateTime) {
        closestPrice = dataPoint.close;
      } else {
        break;
      }
    }

    return closestPrice;
  }

  /**
   * Run portfolio backtest
   */
  async runPortfolioBacktest(portfolioData, provider = 'ALPHA_VANTAGE') {
    const { investmentAmount, startDate, stocks } = portfolioData;
    
    console.error(`[API] Running portfolio backtest from ${startDate}`);
    
    const holdings = [];
    
    for (let i = 0; i < stocks.length; i++) {
      const stock = stocks[i];
      console.error(`[API] Processing ${stock.symbol} (${i + 1}/${stocks.length})`);
      
      try {
        // Get current quote
        const currentQuote = await this.getStockQuote(stock.symbol, provider);
        
        // Add delay for rate limiting
        if (i < stocks.length - 1) {
          await this.delay(1000);
        }
        
        // Get historical price
        const historicalPrice = await this.getHistoricalPrice(stock.symbol, startDate, provider);
        
        if (!historicalPrice) {
          throw new Error(`Unable to get historical price for ${stock.symbol} on ${startDate}`);
        }

        const investedAmount = (investmentAmount * stock.allocation) / 100;
        const shares = investedAmount / historicalPrice;
        const currentValue = shares * currentQuote.price;
        const gainLoss = currentValue - investedAmount;
        const returnPercent = (gainLoss / investedAmount) * 100;

        holdings.push({
          symbol: stock.symbol,
          allocation: stock.allocation,
          shares,
          startPrice: historicalPrice,
          currentPrice: currentQuote.price,
          investedAmount,
          currentValue,
          gainLoss,
          returnPercent
        });
        
      } catch (stockError) {
        console.error(`[API] Error processing ${stock.symbol}:`, stockError);
        throw new Error(`Failed to get data for ${stock.symbol}: ${stockError.message}`);
      }
    }

    const currentValue = holdings.reduce((sum, holding) => sum + holding.currentValue, 0);
    const totalGainLoss = currentValue - investmentAmount;
    const totalGainLossPercent = (totalGainLoss / investmentAmount) * 100;

    return {
      totalInvestment: investmentAmount,
      currentValue,
      totalGainLoss,
      totalGainLossPercent,
      holdings,
      startDate,
      endDate: new Date().toISOString().split('T')[0]
    };
  }

  /**
   * Utility function for delays
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
