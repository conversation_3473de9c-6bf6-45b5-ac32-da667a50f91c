.stock-chart {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.stock-chart.loading {
  text-align: center;
  padding: 3rem;
}

.stock-chart.error {
  background-color: #fee;
  border: 1px solid #fcc;
  text-align: center;
}

.stock-chart.error h3 {
  color: #c33;
  margin-bottom: 0.5rem;
}

.stock-chart.error p {
  color: #a33;
}

.chart-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.chart-header h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin: 0;
}

.chart-container {
  margin: 1rem 0;
  min-height: 400px;
}

.chart-info {
  text-align: center;
  margin-top: 1rem;
}

.chart-info p {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

@media (max-width: 768px) {
  .stock-chart {
    padding: 1rem;
  }
  
  .chart-container {
    min-height: 300px;
  }
  
  .chart-header h3 {
    font-size: 1.2rem;
  }
}
