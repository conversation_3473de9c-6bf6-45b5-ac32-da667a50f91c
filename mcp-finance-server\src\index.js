#!/usr/bin/env node

/**
 * MCP Finance Server - Educational Example
 * 
 * This is a Model Context Protocol (MCP) server that provides stock market data tools.
 * It demonstrates how to:
 * 1. Create an MCP server
 * 2. Define tools that AI assistants can use
 * 3. Handle tool calls and return structured data
 * 4. Integrate with external APIs (stock data)
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { 
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

import { FinanceAPI } from './finance-api.js';
import { config } from './config.js';

/**
 * MCP Server Class
 * This wraps our finance functionality and exposes it as MCP tools
 */
class FinanceMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'finance-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.financeAPI = new FinanceAPI();
    this.setupToolHandlers();
  }

  /**
   * Define the tools that AI assistants can use
   * Each tool has a name, description, and input schema
   */
  setupToolHandlers() {
    // Handle tool listing - tells the AI what tools are available
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'get_stock_quote',
            description: 'Get current stock price and basic information for a symbol',
            inputSchema: {
              type: 'object',
              properties: {
                symbol: {
                  type: 'string',
                  description: 'Stock symbol (e.g., AAPL, GOOGL, TSLA)',
                },
                provider: {
                  type: 'string',
                  description: 'API provider to use',
                  enum: ['ALPHA_VANTAGE', 'RAPIDAPI'],
                  default: 'ALPHA_VANTAGE'
                }
              },
              required: ['symbol'],
            },
          },
          {
            name: 'get_stock_history',
            description: 'Get historical stock data for analysis and charting',
            inputSchema: {
              type: 'object',
              properties: {
                symbol: {
                  type: 'string',
                  description: 'Stock symbol (e.g., AAPL, GOOGL, TSLA)',
                },
                provider: {
                  type: 'string',
                  description: 'API provider to use',
                  enum: ['ALPHA_VANTAGE', 'RAPIDAPI'],
                  default: 'ALPHA_VANTAGE'
                }
              },
              required: ['symbol'],
            },
          },
          {
            name: 'analyze_portfolio',
            description: 'Analyze a portfolio of stocks with allocations and calculate performance',
            inputSchema: {
              type: 'object',
              properties: {
                portfolio: {
                  type: 'array',
                  description: 'Array of stocks with symbols and allocations',
                  items: {
                    type: 'object',
                    properties: {
                      symbol: { type: 'string' },
                      allocation: { type: 'number', minimum: 0, maximum: 100 }
                    },
                    required: ['symbol', 'allocation']
                  }
                },
                investment_amount: {
                  type: 'number',
                  description: 'Total investment amount in USD',
                  default: 10000
                },
                start_date: {
                  type: 'string',
                  description: 'Start date for analysis (YYYY-MM-DD)',
                  pattern: '^\\d{4}-\\d{2}-\\d{2}$'
                },
                provider: {
                  type: 'string',
                  description: 'API provider to use',
                  enum: ['ALPHA_VANTAGE', 'RAPIDAPI'],
                  default: 'ALPHA_VANTAGE'
                }
              },
              required: ['portfolio', 'start_date'],
            },
          },
        ],
      };
    });

    // Handle tool execution - this is where the actual work happens
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'get_stock_quote':
            return await this.handleGetStockQuote(args);
          
          case 'get_stock_history':
            return await this.handleGetStockHistory(args);
          
          case 'analyze_portfolio':
            return await this.handleAnalyzePortfolio(args);
          
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  /**
   * Tool Handler: Get Stock Quote
   * Returns current stock price and basic info
   */
  async handleGetStockQuote(args) {
    const { symbol, provider = 'ALPHA_VANTAGE' } = args;
    
    console.error(`[MCP] Getting quote for ${symbol} using ${provider}`);
    
    const quote = await this.financeAPI.getStockQuote(symbol, provider);
    
    return {
      content: [
        {
          type: 'text',
          text: `Stock Quote for ${symbol}:\n` +
                `Price: $${quote.price}\n` +
                `Change: ${quote.change >= 0 ? '+' : ''}$${quote.change} (${quote.changePercent})\n` +
                `Volume: ${quote.volume?.toLocaleString() || 'N/A'}\n` +
                `Provider: ${provider}`,
        },
      ],
    };
  }

  /**
   * Tool Handler: Get Stock History
   * Returns historical data for analysis
   */
  async handleGetStockHistory(args) {
    const { symbol, provider = 'ALPHA_VANTAGE' } = args;
    
    console.error(`[MCP] Getting history for ${symbol} using ${provider}`);
    
    const history = await this.financeAPI.getDailyStockData(symbol, provider);
    
    // Format the data for display
    const recentData = history.slice(0, 10); // Last 10 days
    const dataText = recentData.map(day => 
      `${day.date}: Open $${day.open}, High $${day.high}, Low $${day.low}, Close $${day.close}`
    ).join('\n');
    
    return {
      content: [
        {
          type: 'text',
          text: `Historical Data for ${symbol} (Last 10 days):\n\n${dataText}\n\n` +
                `Total data points available: ${history.length}\n` +
                `Provider: ${provider}`,
        },
      ],
    };
  }

  /**
   * Tool Handler: Analyze Portfolio
   * Performs portfolio backtesting and analysis
   */
  async handleAnalyzePortfolio(args) {
    const { 
      portfolio, 
      investment_amount = 10000, 
      start_date, 
      provider = 'ALPHA_VANTAGE' 
    } = args;
    
    console.error(`[MCP] Analyzing portfolio from ${start_date} with ${portfolio.length} stocks`);
    
    const portfolioData = {
      investmentAmount: investment_amount,
      startDate: start_date,
      stocks: portfolio
    };
    
    const results = await this.financeAPI.runPortfolioBacktest(portfolioData, provider);
    
    // Format results for display
    const holdingsText = results.holdings.map(holding => 
      `${holding.symbol}: ${holding.allocation}% | ` +
      `$${holding.investedAmount.toFixed(2)} → $${holding.currentValue.toFixed(2)} | ` +
      `${holding.returnPercent >= 0 ? '+' : ''}${holding.returnPercent.toFixed(2)}%`
    ).join('\n');
    
    return {
      content: [
        {
          type: 'text',
          text: `Portfolio Analysis Results:\n\n` +
                `Period: ${results.startDate} to ${results.endDate}\n` +
                `Initial Investment: $${results.totalInvestment.toLocaleString()}\n` +
                `Current Value: $${results.currentValue.toLocaleString()}\n` +
                `Total Gain/Loss: ${results.totalGainLoss >= 0 ? '+' : ''}$${results.totalGainLoss.toLocaleString()}\n` +
                `Total Return: ${results.totalGainLossPercent >= 0 ? '+' : ''}${results.totalGainLossPercent.toFixed(2)}%\n\n` +
                `Holdings Breakdown:\n${holdingsText}\n\n` +
                `Provider: ${provider}`,
        },
      ],
    };
  }

  /**
   * Start the MCP server
   */
  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('[MCP] Finance server started and ready for connections');
  }
}

// Start the server
const server = new FinanceMCPServer();
server.start().catch((error) => {
  console.error('[MCP] Failed to start server:', error);
  process.exit(1);
});
