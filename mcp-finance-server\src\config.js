/**
 * Configuration Module
 * 
 * Handles loading API keys and configuration from environment variables.
 * This keeps sensitive information secure and separate from code.
 */

import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Load API key from file or environment variable
 */
function loadApiKey(keyName, filePath = null) {
  // First try environment variable
  const envKey = process.env[keyName];
  if (envKey && !envKey.includes('your-')) {
    return envKey;
  }

  // Then try loading from file (if path provided)
  if (filePath) {
    try {
      const key = readFileSync(filePath, 'utf8').trim();
      if (key && !key.includes('your-')) {
        return key;
      }
    } catch (error) {
      console.error(`[Config] Could not read API key from ${filePath}:`, error.message);
    }
  }

  // Return placeholder if not found
  return `your-${keyName.toLowerCase()}-key-here`;
}

/**
 * Configuration object
 */
export const config = {
  // API Keys - loaded from environment or files
  apiKeys: {
    ALPHA_VANTAGE: loadApiKey(
      'ALPHA_VANTAGE_API_KEY',
      process.env.ALPHA_VANTAGE_KEY_FILE || 'C:/Dev/APIKeys/alphavantage.txt'
    ),
    RAPIDAPI: loadApiKey(
      'RAPIDAPI_KEY',
      process.env.RAPIDAPI_KEY_FILE || 'C:/Dev/APIKeys/rapidapi.txt'
    )
  },

  // Server configuration
  server: {
    name: 'finance-server',
    version: '1.0.0',
    description: 'MCP Server for Stock Market Data'
  },

  // API rate limiting
  rateLimits: {
    delayBetweenCalls: 1000, // 1 second between API calls
    maxRetries: 3
  },

  // Default values
  defaults: {
    provider: 'ALPHA_VANTAGE',
    investmentAmount: 10000,
    portfolioSize: 5
  }
};

/**
 * Validate configuration
 */
export function validateConfig() {
  const issues = [];

  // Check if at least one API key is configured
  const hasValidKey = Object.values(config.apiKeys).some(key => 
    key && !key.includes('your-')
  );

  if (!hasValidKey) {
    issues.push('No valid API keys configured. Please set up at least one API key.');
  }

  // Check individual keys
  if (config.apiKeys.ALPHA_VANTAGE.includes('your-')) {
    issues.push('Alpha Vantage API key not configured');
  }

  if (config.apiKeys.RAPIDAPI.includes('your-')) {
    issues.push('RapidAPI key not configured');
  }

  return {
    valid: issues.length === 0,
    issues
  };
}

/**
 * Get available providers (those with valid API keys)
 */
export function getAvailableProviders() {
  const providers = [];

  if (!config.apiKeys.ALPHA_VANTAGE.includes('your-')) {
    providers.push({
      key: 'ALPHA_VANTAGE',
      name: 'Alpha Vantage',
      description: 'Free tier: 5 calls/minute, 500 calls/day'
    });
  }

  if (!config.apiKeys.RAPIDAPI.includes('your-')) {
    providers.push({
      key: 'RAPIDAPI',
      name: 'Yahoo Finance (RapidAPI)',
      description: 'Freemium tier with higher rate limits'
    });
  }

  return providers;
}

/**
 * Get default provider (first available)
 */
export function getDefaultProvider() {
  const available = getAvailableProviders();
  return available.length > 0 ? available[0].key : 'ALPHA_VANTAGE';
}

// Log configuration status on import
const validation = validateConfig();
const availableProviders = getAvailableProviders();

console.error('[Config] Configuration loaded:');
console.error(`[Config] Available providers: ${availableProviders.map(p => p.name).join(', ') || 'None'}`);
console.error(`[Config] Default provider: ${getDefaultProvider()}`);

if (!validation.valid) {
  console.error('[Config] Configuration issues:');
  validation.issues.forEach(issue => console.error(`[Config] - ${issue}`));
}
