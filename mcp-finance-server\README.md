# MCP Finance Server 📈

An educational **Model Context Protocol (MCP) server** that provides stock market data tools to AI assistants. This project demonstrates how to wrap existing API functionality as an MCP server that can be used by <PERSON> and other MCP-compatible AI tools.

## 🎯 What is MCP?

**Model Context Protocol (MCP)** is a standard that allows AI assistants to securely connect to external data sources and tools. Think of it as a way to give AI assistants "superpowers" by letting them:

- Access real-time data (like stock prices)
- Perform calculations (like portfolio analysis)
- Interact with external services
- Use specialized tools

## 🚀 What This Server Provides

This MCP server exposes three main tools that AI assistants can use:

### 1. `get_stock_quote`
Get current stock price and basic information
```
Input: { symbol: "AAPL", provider: "ALPHA_VANTAGE" }
Output: Current price, change, volume, etc.
```

### 2. `get_stock_history` 
Get historical stock data for analysis
```
Input: { symbol: "AAPL", provider: "ALPHA_VANTAGE" }
Output: Historical OHLCV data for charting/analysis
```

### 3. `analyze_portfolio`
Analyze a portfolio with backtesting
```
Input: { 
  portfolio: [
    { symbol: "AAPL", allocation: 40 },
    { symbol: "GOOGL", allocation: 60 }
  ],
  investment_amount: 10000,
  start_date: "2024-01-01"
}
Output: Portfolio performance analysis
```

## 📋 Prerequisites

- **Node.js 18+** installed
- **API Keys** for stock data:
  - [Alpha Vantage](https://www.alphavantage.co/support/#api-key) (free)
  - [RapidAPI](https://rapidapi.com/apidojo/api/yahoo-finance1) (optional)

## 🛠️ Setup Instructions

### 1. Install Dependencies
```bash
cd mcp-finance-server
npm install
```

### 2. Configure API Keys

**Option A: Environment Variables**
```bash
# Copy the example file
cp .env.example .env

# Edit .env and add your API keys
ALPHA_VANTAGE_API_KEY=your-actual-key-here
RAPIDAPI_KEY=your-rapidapi-key-here
```

**Option B: File-based (like your React app)**
```bash
# Create API key files
mkdir -p C:/Dev/APIKeys
echo "your-alpha-vantage-key" > C:/Dev/APIKeys/alphavantage.txt
echo "your-rapidapi-key" > C:/Dev/APIKeys/rapidapi.txt
```

### 3. Test the Server
```bash
# Test the finance API functionality
npm test

# Expected output:
# ✅ Available providers: Alpha Vantage
# ✅ Quote: $150.25 (+$2.15)
# ✅ Historical data: 100 days available
# ✅ Portfolio analysis complete
```

### 4. Start the MCP Server
```bash
npm start
```

## 🔌 Using with Claude Desktop

### 1. Configure Claude Desktop

Add this to your Claude Desktop configuration file:

**Windows:** `%APPDATA%/Claude/claude_desktop_config.json`
**Mac:** `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "finance": {
      "command": "node",
      "args": ["C:/path/to/mcp-finance-server/src/index.js"],
      "env": {
        "ALPHA_VANTAGE_API_KEY": "your-key-here"
      }
    }
  }
}
```

### 2. Restart Claude Desktop

### 3. Test with Claude

Try asking Claude:
- "Get me a quote for Apple stock"
- "Analyze a portfolio with 60% AAPL and 40% GOOGL from January 1st, 2024"
- "Show me the historical data for Tesla"

## 📚 How MCP Works (Educational)

### 1. **Server Structure**
```
MCP Server
├── Tools Definition (what the AI can do)
├── Tool Handlers (how to do it)
└── Transport Layer (communication)
```

### 2. **Communication Flow**
```
Claude Desktop → MCP Server → Finance APIs → Results → Claude
```

### 3. **Key Components**

**Tools Definition:**
```javascript
{
  name: 'get_stock_quote',
  description: 'Get current stock price',
  inputSchema: { /* JSON Schema */ }
}
```

**Tool Handler:**
```javascript
async handleGetStockQuote(args) {
  const quote = await this.financeAPI.getStockQuote(args.symbol);
  return { content: [{ type: 'text', text: `Price: $${quote.price}` }] };
}
```

## 🔧 Development

### Project Structure
```
mcp-finance-server/
├── src/
│   ├── index.js          # Main MCP server
│   ├── finance-api.js    # Finance functionality (from React app)
│   ├── config.js         # Configuration management
│   └── test.js           # Test script
├── package.json          # Dependencies
├── .env.example          # Environment template
└── README.md            # This file
```

### Adding New Tools

1. **Define the tool** in `setupToolHandlers()`:
```javascript
{
  name: 'my_new_tool',
  description: 'What it does',
  inputSchema: { /* schema */ }
}
```

2. **Add handler** in `CallToolRequestSchema`:
```javascript
case 'my_new_tool':
  return await this.handleMyNewTool(args);
```

3. **Implement handler**:
```javascript
async handleMyNewTool(args) {
  // Your logic here
  return { content: [{ type: 'text', text: 'Result' }] };
}
```

## 🐛 Troubleshooting

### "No valid API keys configured"
- Check your `.env` file or API key files
- Ensure keys don't contain "your-" placeholder text

### "Rate limit exceeded"
- Alpha Vantage: 5 calls/minute, 500/day (free tier)
- Add delays between calls in portfolio analysis

### "Module not found"
- Run `npm install` to install dependencies
- Check Node.js version (requires 18+)

## 🎓 Learning Resources

- [MCP Documentation](https://modelcontextprotocol.io/)
- [MCP SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- [Claude Desktop MCP Guide](https://docs.anthropic.com/claude/docs/mcp)

## 🤝 Contributing

This is an educational project! Feel free to:
- Add new financial tools
- Improve error handling
- Add more API providers
- Enhance documentation

## 📄 License

MIT License - feel free to use this as a learning resource!
