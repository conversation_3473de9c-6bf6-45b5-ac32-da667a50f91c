.portfolio-builder {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.portfolio-builder h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.portfolio-builder p {
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.sample-portfolio-info {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border: 1px solid #27ae60;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.sample-portfolio-info h4 {
  margin: 0 0 1rem 0;
  color: #27ae60;
  font-size: 1.1rem;
  text-align: center;
}

.sample-portfolio-info p {
  margin: 0.5rem 0;
  color: #2c3e50;
  line-height: 1.5;
}

.sample-portfolio-info em {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.portfolio-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 1.5rem;
  background-color: #fafbfc;
}

.form-section h3 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.form-input {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
}

.form-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.stocks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.allocation-summary {
  font-weight: 600;
  font-size: 1.1rem;
}

.allocation-summary .valid {
  color: #27ae60;
}

.allocation-summary .invalid {
  color: #e74c3c;
}

.stock-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.stock-inputs {
  display: flex;
  gap: 0.75rem;
  flex: 1;
}

.symbol-input {
  flex: 2;
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 1rem;
  text-transform: uppercase;
  transition: border-color 0.3s ease;
}

.symbol-input:focus {
  outline: none;
  border-color: #3498db;
}

.allocation-input-group {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.allocation-input {
  width: 100%;
  padding: 0.75rem;
  padding-right: 2rem;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.allocation-input:focus {
  outline: none;
  border-color: #3498db;
}

.allocation-symbol {
  position: absolute;
  right: 0.75rem;
  color: #7f8c8d;
  font-weight: 600;
  pointer-events: none;
}

.remove-stock-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.remove-stock-btn:hover:not(:disabled) {
  background-color: #c0392b;
}

.remove-stock-btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.stock-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.add-stock-btn, .distribute-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #3498db;
  background-color: transparent;
  color: #3498db;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-stock-btn:hover:not(:disabled), .distribute-btn:hover:not(:disabled) {
  background-color: #3498db;
  color: white;
}

.add-stock-btn:disabled, .distribute-btn:disabled {
  border-color: #bdc3c7;
  color: #bdc3c7;
  cursor: not-allowed;
}

.run-backtest-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  align-self: center;
}

.run-backtest-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.run-backtest-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

@media (max-width: 768px) {
  .portfolio-builder {
    padding: 1rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .stock-inputs {
    flex-direction: column;
  }
  
  .stock-actions {
    flex-direction: column;
  }
  
  .stocks-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
