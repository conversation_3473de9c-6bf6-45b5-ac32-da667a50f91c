import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ApiProvider } from './context/ApiContext'
import Navigation from './components/Navigation'
import StockQuotes from './pages/StockQuotes'
import Portfolio from './pages/Portfolio'
import Config from './pages/Config'
import './App.css'

function App() {
  return (
    <ApiProvider>
      <Router>
        <div className="app">
          <header className="app-header">
            <h1>📈 Stock Analysis Suite</h1>
            <p>Real-time quotes, charts, and portfolio backtesting with multiple API providers</p>
          </header>

          <Navigation />

          <main className="app-main">
            <Routes>
              <Route path="/" element={<Config />} />
              <Route path="/config" element={<Config />} />
              <Route path="/quotes" element={<StockQuotes />} />
              <Route path="/portfolio" element={<Portfolio />} />
            </Routes>
          </main>

          <footer className="app-footer">
            <p>Multi-API Stock Data Platform | Built with React & Vite</p>
          </footer>
        </div>
      </Router>
    </ApiProvider>
  )
}

export default App
