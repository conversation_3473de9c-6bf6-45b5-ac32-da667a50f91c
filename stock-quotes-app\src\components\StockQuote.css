.stock-quote {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.stock-quote.loading {
  text-align: center;
  padding: 3rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stock-quote.error {
  background-color: #fee;
  border: 1px solid #fcc;
  text-align: center;
}

.stock-quote.error h3 {
  color: #c33;
  margin-bottom: 0.5rem;
}

.stock-quote.error p {
  color: #a33;
}

.quote-header {
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
}

.symbol {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 1rem;
  flex-wrap: wrap;
}

.current-price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.change {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
}

.change.positive {
  background-color: #d5f4e6;
  color: #27ae60;
}

.change.negative {
  background-color: #fdeaea;
  color: #e74c3c;
}

.quote-details {
  margin-top: 1.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.detail-item .label {
  font-weight: 600;
  color: #7f8c8d;
}

.detail-item .value {
  font-weight: bold;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .price-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .current-price {
    font-size: 2rem;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
