import { useState, useEffect } from 'react';
import { 
  validateBacktestDate, 
  getCommonBacktestDates, 
  isTradingDay,
  isWeekend,
  isMarketHoliday 
} from '../utils/tradingCalendar';
import './TradingDatePicker.css';

const TradingDatePicker = ({ value, onChange, label = "Start Date" }) => {
  const [validation, setValidation] = useState({ valid: true });
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const commonDates = getCommonBacktestDates();

  useEffect(() => {
    if (value) {
      const result = validateBacktestDate(value);
      setValidation(result);
      
      // Auto-correct to suggested date if invalid
      if (!result.valid && result.suggestion) {
        onChange(result.suggestion);
      }
    }
  }, [value, onChange]);

  const handleDateChange = (e) => {
    const newDate = e.target.value;
    onChange(newDate);
  };

  const handleQuickSelect = (date) => {
    onChange(date);
    setShowSuggestions(false);
  };

  const getDateStatus = (dateString) => {
    if (!dateString) return null;
    
    if (isWeekend(dateString)) {
      return { type: 'weekend', message: 'Weekend - Markets closed' };
    }
    
    if (isMarketHoliday(dateString)) {
      return { type: 'holiday', message: 'Market holiday - Markets closed' };
    }
    
    if (isTradingDay(dateString)) {
      return { type: 'trading', message: 'Valid trading day' };
    }
    
    return null;
  };

  const dateStatus = getDateStatus(value);

  return (
    <div className="trading-date-picker">
      <label htmlFor="trading-date" className="date-label">
        {label}
      </label>
      
      <div className="date-input-container">
        <input
          id="trading-date"
          type="date"
          value={value}
          onChange={handleDateChange}
          className={`date-input ${!validation.valid ? 'invalid' : ''}`}
          max={new Date().toISOString().split('T')[0]}
        />
        
        <button
          type="button"
          onClick={() => setShowSuggestions(!showSuggestions)}
          className="quick-select-button"
          title="Quick select common dates"
        >
          📅
        </button>
      </div>

      {/* Date Status Indicator */}
      {dateStatus && (
        <div className={`date-status ${dateStatus.type}`}>
          <span className="status-icon">
            {dateStatus.type === 'trading' && '✅'}
            {dateStatus.type === 'weekend' && '📅'}
            {dateStatus.type === 'holiday' && '🏛️'}
          </span>
          <span className="status-message">{dateStatus.message}</span>
        </div>
      )}

      {/* Validation Message */}
      {!validation.valid && (
        <div className="validation-message">
          <span className="error-icon">⚠️</span>
          <span className="error-text">{validation.reason}</span>
          {validation.suggestion && (
            <button
              type="button"
              onClick={() => onChange(validation.suggestion)}
              className="suggestion-button"
            >
              Use {validation.suggestion} instead
            </button>
          )}
        </div>
      )}

      {/* Quick Select Dropdown */}
      {showSuggestions && (
        <div className="quick-select-dropdown">
          <div className="dropdown-header">
            <h4>Common Backtest Dates</h4>
            <button
              type="button"
              onClick={() => setShowSuggestions(false)}
              className="close-button"
            >
              ✕
            </button>
          </div>
          
          <div className="date-options">
            {commonDates.map((option, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleQuickSelect(option.date)}
                className="date-option"
              >
                <span className="option-label">{option.label}</span>
                <span className="option-date">{option.date}</span>
              </button>
            ))}
          </div>
          
          <div className="dropdown-footer">
            <small>All dates are valid trading days</small>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradingDatePicker;
