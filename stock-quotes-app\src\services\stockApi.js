import axios from 'axios';

// API key is passed as a parameter from the calling component
// In production, you'd want to use environment variables or a secure backend endpoint

const BASE_URL = 'https://www.alphavantage.co/query';

/**
 * Fetch the current stock quote for the given symbol using Alpha Vantage API.
 * 
 * @param {string} symbol - The stock ticker symbol (e.g., AAPL for Apple)
 * @param {string} apiKey - Your Alpha Vantage API key
 * @returns {Promise<Object|null>} The stock quote data or null if an error occurs
 */
export const getStockQuote = async (symbol, apiKey) => {
  try {
    const response = await axios.get(BASE_URL, {
      params: {
        function: 'GLOBAL_QUOTE',
        symbol: symbol,
        apikey: apiKey
      }
    });

    const data = response.data;
    
    if (data['Global Quote']) {
      const quote = data['Global Quote'];
      return {
        symbol: quote['01. symbol'],
        price: parseFloat(quote['05. price']),
        change: parseFloat(quote['09. change']),
        changePercent: quote['10. change percent'],
        previousClose: parseFloat(quote['08. previous close']),
        open: parseFloat(quote['02. open']),
        high: parseFloat(quote['03. high']),
        low: parseFloat(quote['04. low']),
        volume: parseInt(quote['06. volume']),
        latestTradingDay: quote['07. latest trading day']
      };
    } else {
      console.error(`No data found for symbol '${symbol}'. Please check the ticker symbol.`);
      return null;
    }
  } catch (error) {
    console.error('An error occurred while fetching stock quote:', error);
    return null;
  }
};

/**
 * Fetch daily stock data for the given symbol using Alpha Vantage API.
 * 
 * @param {string} symbol - The stock ticker symbol (e.g., AAPL for Apple)
 * @param {string} apiKey - Your Alpha Vantage API key
 * @returns {Promise<Array|null>} Array of daily stock data or null if an error occurs
 */
export const getDailyStockData = async (symbol, apiKey) => {
  try {
    const response = await axios.get(BASE_URL, {
      params: {
        function: 'TIME_SERIES_DAILY',
        symbol: symbol,
        apikey: apiKey,
        outputsize: 'compact' // Last 100 data points
      }
    });

    const data = response.data;
    
    if (data['Time Series (Daily)']) {
      const timeSeries = data['Time Series (Daily)'];
      
      // Convert to array format similar to your pandas DataFrame
      const dailyData = Object.entries(timeSeries).map(([date, values]) => ({
        date: date,
        open: parseFloat(values['1. open']),
        high: parseFloat(values['2. high']),
        low: parseFloat(values['3. low']),
        close: parseFloat(values['4. close']),
        volume: parseInt(values['5. volume'])
      }));

      // Sort by date (most recent first)
      return dailyData.sort((a, b) => new Date(b.date) - new Date(a.date));
    } else {
      console.error(`No data found for symbol '${symbol}'. Please check the ticker symbol.`);
      return null;
    }
  } catch (error) {
    console.error('An error occurred while fetching daily stock data:', error);
    return null;
  }
};

/**
 * Get historical stock price for a specific date
 *
 * @param {string} symbol - The stock ticker symbol
 * @param {string} targetDate - The target date in YYYY-MM-DD format
 * @param {string} apiKey - Your Alpha Vantage API key
 * @returns {Promise<number|null>} The stock price on that date or null if not found
 */
export const getHistoricalPrice = async (symbol, targetDate, apiKey) => {
  try {
    console.log(`Getting historical price for ${symbol} on ${targetDate}`);

    // Get daily data and find the price for the target date
    const dailyData = await getDailyStockData(symbol, apiKey);

    if (!dailyData || dailyData.length === 0) {
      console.error(`No daily data available for ${symbol}`);
      return null;
    }

    console.log(`Got ${dailyData.length} data points for ${symbol}`);

    // Find the exact date or the closest trading day before it
    const targetDateTime = new Date(targetDate).getTime();

    // Sort by date (oldest first) and find the closest date on or before target
    const sortedData = dailyData.sort((a, b) => new Date(a.date) - new Date(b.date));

    let closestPrice = null;
    let closestDate = null;

    for (const dataPoint of sortedData) {
      const dataPointTime = new Date(dataPoint.date).getTime();
      if (dataPointTime <= targetDateTime) {
        closestPrice = dataPoint.close;
        closestDate = dataPoint.date;
      } else {
        break;
      }
    }

    if (closestPrice) {
      console.log(`Found historical price for ${symbol}: $${closestPrice} on ${closestDate}`);
    } else {
      console.error(`No historical price found for ${symbol} on or before ${targetDate}`);
    }

    return closestPrice;
  } catch (error) {
    console.error(`Error fetching historical price for ${symbol} on ${targetDate}:`, error);
    return null;
  }
};

/**
 * Run portfolio backtest analysis
 *
 * @param {Object} portfolioData - Portfolio configuration
 * @param {string} apiKey - Your Alpha Vantage API key
 * @returns {Promise<Object|null>} Backtest results or null if error
 */
// Helper function to add delay between API calls
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const runPortfolioBacktest = async (portfolioData, apiKey) => {
  try {
    const { investmentAmount, startDate, stocks } = portfolioData;

    console.log('Starting portfolio backtest for:', stocks.map(s => s.symbol));

    // Process stocks sequentially to avoid rate limiting
    const holdings = [];

    for (let i = 0; i < stocks.length; i++) {
      const stock = stocks[i];
      console.log(`Processing ${stock.symbol} (${i + 1}/${stocks.length})`);

      try {
        // Get current quote first
        console.log(`Fetching current quote for ${stock.symbol}`);
        const currentQuote = await getStockQuote(stock.symbol, apiKey);

        if (!currentQuote) {
          throw new Error(`Unable to get current quote for ${stock.symbol}`);
        }

        // Add delay before next API call
        await delay(1000); // 1 second delay

        // Get historical price
        console.log(`Fetching historical price for ${stock.symbol} on ${startDate}`);
        const historicalPrice = await getHistoricalPrice(stock.symbol, startDate, apiKey);

        if (!historicalPrice) {
          throw new Error(`Unable to get historical price for ${stock.symbol} on ${startDate}`);
        }

        console.log(`${stock.symbol}: Current=$${currentQuote.price}, Historical=$${historicalPrice}`);

        const investedAmount = (investmentAmount * stock.allocation) / 100;
        const shares = investedAmount / historicalPrice;
        const currentValue = shares * currentQuote.price;
        const gainLoss = currentValue - investedAmount;
        const returnPercent = (gainLoss / investedAmount) * 100;

        holdings.push({
          symbol: stock.symbol,
          allocation: stock.allocation,
          shares,
          startPrice: historicalPrice,
          currentPrice: currentQuote.price,
          investedAmount,
          currentValue,
          gainLoss,
          returnPercent
        });

        // Add delay before processing next stock
        if (i < stocks.length - 1) {
          await delay(1000);
        }

      } catch (stockError) {
        console.error(`Error processing ${stock.symbol}:`, stockError);
        throw new Error(`Failed to get data for ${stock.symbol}: ${stockError.message}`);
      }
    }

    const currentValue = holdings.reduce((sum, holding) => sum + holding.currentValue, 0);
    const totalGainLoss = currentValue - investmentAmount;
    const totalGainLossPercent = (totalGainLoss / investmentAmount) * 100;

    return {
      portfolio: portfolioData,
      totalInvestment: investmentAmount,
      currentValue,
      totalGainLoss,
      totalGainLossPercent,
      holdings,
      startDate,
      endDate: new Date().toISOString().split('T')[0]
    };

  } catch (error) {
    console.error('Error running portfolio backtest:', error);
    throw error;
  }
};
