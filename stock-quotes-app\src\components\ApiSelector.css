.api-selector {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.api-selector.error {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  padding: 1.5rem;
}

.api-selector.error h3 {
  margin: 0 0 1rem 0;
  color: white;
}

.api-selector.error ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.api-selector.error li {
  margin-bottom: 0.5rem;
}

.api-selector.error a {
  color: #fff;
  text-decoration: underline;
  margin-left: 0.5rem;
}

.selector-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.selector-header label {
  font-weight: 600;
  font-size: 1rem;
  white-space: nowrap;
}

.provider-select {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: white;
  color: #2c3e50;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
}

.provider-select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.provider-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.9rem;
  opacity: 0.9;
}

.provider-status {
  font-weight: 500;
}

.provider-link {
  color: white;
  text-decoration: none;
  padding: 0.25rem 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.provider-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

@media (max-width: 768px) {
  .selector-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .provider-select {
    width: 100%;
  }
  
  .provider-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
