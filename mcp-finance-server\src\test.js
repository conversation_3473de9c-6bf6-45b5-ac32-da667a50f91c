#!/usr/bin/env node

/**
 * Test Script for MCP Finance Server
 * 
 * This script tests the finance API functionality without the MCP layer.
 * Useful for debugging and understanding how the API works.
 */

import { FinanceAPI } from './finance-api.js';
import { config, validateConfig, getAvailableProviders } from './config.js';

async function runTests() {
  console.log('🧪 Testing MCP Finance Server Components\n');

  // Test configuration
  console.log('📋 Configuration Test:');
  const validation = validateConfig();
  const providers = getAvailableProviders();
  
  console.log(`✅ Available providers: ${providers.map(p => p.name).join(', ') || 'None'}`);
  
  if (!validation.valid) {
    console.log('❌ Configuration issues:');
    validation.issues.forEach(issue => console.log(`   - ${issue}`));
    console.log('\n⚠️  Please configure API keys to run full tests.\n');
    return;
  }

  const financeAPI = new FinanceAPI();
  const provider = providers[0].key; // Use first available provider

  try {
    // Test 1: Stock Quote
    console.log('\n📈 Test 1: Stock Quote');
    console.log(`Getting quote for AAPL using ${provider}...`);
    
    const quote = await financeAPI.getStockQuote('AAPL', provider);
    console.log(`✅ Quote: $${quote.price} (${quote.change >= 0 ? '+' : ''}${quote.change})`);

    // Test 2: Historical Data
    console.log('\n📊 Test 2: Historical Data');
    console.log(`Getting historical data for AAPL using ${provider}...`);
    
    const history = await financeAPI.getDailyStockData('AAPL', provider);
    console.log(`✅ Historical data: ${history.length} days available`);
    console.log(`   Latest: ${history[0].date} - Close: $${history[0].close}`);

    // Test 3: Portfolio Analysis
    console.log('\n💼 Test 3: Portfolio Analysis');
    console.log('Analyzing sample portfolio...');
    
    const portfolioData = {
      investmentAmount: 10000,
      startDate: '2024-01-02',
      stocks: [
        { symbol: 'AAPL', allocation: 40 },
        { symbol: 'GOOGL', allocation: 30 },
        { symbol: 'MSFT', allocation: 30 }
      ]
    };

    const results = await financeAPI.runPortfolioBacktest(portfolioData, provider);
    console.log(`✅ Portfolio analysis complete:`);
    console.log(`   Initial: $${results.totalInvestment.toLocaleString()}`);
    console.log(`   Current: $${results.currentValue.toLocaleString()}`);
    console.log(`   Return: ${results.totalGainLossPercent >= 0 ? '+' : ''}${results.totalGainLossPercent.toFixed(2)}%`);

    console.log('\n🎉 All tests passed! The MCP server should work correctly.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('   Check your API keys and network connection.');
  }
}

// Run tests
runTests().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});
