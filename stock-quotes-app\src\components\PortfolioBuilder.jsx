import { useState, useEffect } from 'react';
import TradingDatePicker from './TradingDatePicker';
import CountrySelector from './CountrySelector';
import { getClosestTradingDay } from '../utils/tradingCalendar';
import './PortfolioBuilder.css';

const PortfolioBuilder = ({ onRunBacktest, isLoading }) => {
  const [investmentAmount, setInvestmentAmount] = useState(10000);
  const [country, setCountry] = useState('UK'); // Default to UK for ETF portfolio
  const [startDate, setStartDate] = useState(() => {
    // Initialize with 1 year ago for default 1-year return
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    return getClosestTradingDay(oneYearAgo.toISOString().split('T')[0], 'UK');
  });

  // Initialize with sample UK ETF portfolio
  const [stocks, setStocks] = useState([
    { symbol: 'ERNS', allocation: 30 }, // 30% ERNS
    { symbol: 'VWRL', allocation: 40 }, // 40% VWRL
    { symbol: 'ISF', allocation: 10 },  // 10% ISF
    { symbol: 'IMEU', allocation: 10 }, // 10% IMEU
    { symbol: 'IGLS', allocation: 10 }  // 10% IGLS
  ]);

  // Calculate total allocation
  const totalAllocation = stocks.reduce((sum, stock) => sum + (stock.allocation || 0), 0);

  const addStock = () => {
    if (stocks.length < 5) {
      setStocks([...stocks, { symbol: '', allocation: 0 }]);
    }
  };

  const removeStock = (index) => {
    if (stocks.length > 1) {
      setStocks(stocks.filter((_, i) => i !== index));
    }
  };

  const updateStock = (index, field, value) => {
    const updatedStocks = stocks.map((stock, i) => {
      if (i === index) {
        return { ...stock, [field]: field === 'allocation' ? parseFloat(value) || 0 : value.toUpperCase() };
      }
      return stock;
    });
    setStocks(updatedStocks);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validation
    const validStocks = stocks.filter(stock => stock.symbol.trim() && stock.allocation > 0);
    
    if (validStocks.length === 0) {
      alert('Please add at least one stock with allocation > 0%');
      return;
    }
    
    if (Math.abs(totalAllocation - 100) > 0.01) {
      alert('Total allocation must equal 100%');
      return;
    }

    const portfolioData = {
      investmentAmount,
      startDate,
      country, // Include country for market-specific holidays
      stocks: validStocks
    };

    onRunBacktest(portfolioData);
  };

  // Auto-distribute remaining allocation
  const distributeRemaining = () => {
    const validStocks = stocks.filter(stock => stock.symbol.trim());
    if (validStocks.length === 0) return;

    const remaining = 100 - totalAllocation;
    const perStock = remaining / validStocks.length;
    
    const updatedStocks = stocks.map(stock => {
      if (stock.symbol.trim()) {
        return { ...stock, allocation: (stock.allocation || 0) + perStock };
      }
      return stock;
    });
    
    setStocks(updatedStocks);
  };

  return (
    <div className="portfolio-builder">
      <h2>📊 Portfolio Backtest Builder</h2>
      <p>Create a portfolio and see how it would have performed from your chosen start date.</p>

      <div className="sample-portfolio-info">
        <h4>🎯 Sample UK ETF Portfolio Loaded</h4>
        <p>
          <strong>Diversified Global ETF Portfolio:</strong> 30% ERNS (Emerging Markets), 40% VWRL (World),
          10% ISF (Core FTSE 100), 10% IMEU (Europe), 10% IGLS (Global Government Bonds)
        </p>
        <p><em>Default period: 1 year return. Modify as needed for your analysis.</em></p>
      </div>
      
      <form onSubmit={handleSubmit} className="portfolio-form">
        <div className="form-section">
          <h3>Investment Details</h3>
          <div className="form-row">
            <div className="form-group">
              <CountrySelector
                value={country}
                onChange={setCountry}
                label="Market"
              />
            </div>
            <div className="form-group">
              <label htmlFor="investment">Total Investment (USD)</label>
              <input
                id="investment"
                type="number"
                min="100"
                step="100"
                value={investmentAmount}
                onChange={(e) => setInvestmentAmount(parseFloat(e.target.value) || 0)}
                className="form-input"
                disabled={isLoading}
              />
            </div>
            <div className="form-group">
              <TradingDatePicker
                value={startDate}
                onChange={setStartDate}
                label="Backtest Start Date"
                disabled={isLoading}
              />
            </div>
          </div>
        </div>

        <div className="form-section">
          <div className="stocks-header">
            <h3>Stock Allocations</h3>
            <div className="allocation-summary">
              Total: <span className={totalAllocation === 100 ? 'valid' : 'invalid'}>{totalAllocation.toFixed(1)}%</span>
            </div>
          </div>
          
          {stocks.map((stock, index) => (
            <div key={index} className="stock-row">
              <div className="stock-inputs">
                <input
                  type="text"
                  placeholder="Stock Symbol (e.g., AAPL)"
                  value={stock.symbol}
                  onChange={(e) => updateStock(index, 'symbol', e.target.value)}
                  className="symbol-input"
                  disabled={isLoading}
                />
                <div className="allocation-input-group">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    placeholder="0"
                    value={stock.allocation || ''}
                    onChange={(e) => updateStock(index, 'allocation', e.target.value)}
                    className="allocation-input"
                    disabled={isLoading}
                  />
                  <span className="allocation-symbol">%</span>
                </div>
              </div>
              {stocks.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeStock(index)}
                  className="remove-stock-btn"
                  disabled={isLoading}
                >
                  ✕
                </button>
              )}
            </div>
          ))}
          
          <div className="stock-actions">
            {stocks.length < 5 && (
              <button
                type="button"
                onClick={addStock}
                className="add-stock-btn"
                disabled={isLoading}
              >
                + Add Stock
              </button>
            )}
            {totalAllocation < 100 && (
              <button
                type="button"
                onClick={distributeRemaining}
                className="distribute-btn"
                disabled={isLoading}
              >
                Auto-distribute Remaining {(100 - totalAllocation).toFixed(1)}%
              </button>
            )}
          </div>
        </div>

        <button 
          type="submit" 
          className="run-backtest-btn"
          disabled={isLoading || totalAllocation !== 100}
        >
          {isLoading ? 'Running Backtest...' : 'Run Backtest'}
        </button>
      </form>
    </div>
  );
};

export default PortfolioBuilder;
